/**
 * @definition    addOrUpdateResultV2
 * @description   结果表入库（MERGE/UPDATE；含STATUS/DOC_INFO/审计） wanghq
 * @implementation    {Script}
 *
 * @param    {STRING}    tableName
 * @param    {NUMBER}    id              {"aspect.defaultValue":"0"}
 * @param    {STRING}    NODECODE
 * @param    {STRING}    NODENAME
 * @param    {STRING}    FILE_NUMBER
 * @param    {STRING}    FILE_NAME
 * @param    {STRING}    FILE_TYPE
 * @param    {STRING}    GATHERING_METHOD
 * @param    {STRING}    SOURCE_SYSTEM
 * @param    {STRING}    DELIVERY_STATE
 * @param    {STRING}    SECURITY_LEVEL
 * @param    {STRING}    FILEPATH
 * @param    {STRING}    FILENAME
 * @param    {STRING}    STATE_CHECK
 * @param    {STRING}    FILE_FORMAT
 * @param    {STRING}    productId
 * @param    {STRING}    STATUS
 * @param    {STRING}    docInfo         {"aspect.defaultValue":""}
 *
 * @returns    {NUMBER}
 */

var nowStr = Things['Thing.Integration.DataCollectV2'].getNowStrV2();

// 仅当 id==0 时获取序列号；否则沿用传入的 id 做更新
var newId = id;
if (newId == 0) {
	var seqName = "";
	if (tableName == "DESIGN_DATA_RESULT") {
		seqName = "design_cm_sequence.nextval";
	} else if (tableName == "CRAFT_DATA_RESULT") {
		seqName = "craft_cm_sequence.nextval";
	} else if (tableName == "PROCESS_CONTROL_RESULT") {
		seqName = "process_cm_sequence.nextval";
	} else if (tableName == "QUALITY_CONTROL_RESULT") {
		seqName = "quality_cm_sequence.nextval";
	}
	var sqlSeq = "select " + seqName + " AS NEXTVAL from dual";
	newId = Things["Thing.DB.Oracle"].RunQuery({ sql: sqlSeq }).getRow(0).NEXTVAL;
}

// MERGE upsert
var sql = "merge into " + tableName + " t using (select '" + newId + "' as ID from dual) s on (t.id = to_number(s.id)) " +
	"when matched then update set " +
	"t.NODECODE='" + NODECODE + "',t.NODENAME='" + NODENAME + "',t.FILE_NUMBER='" + FILE_NUMBER + "'," +
	"t.FILE_NAME='" + FILE_NAME + "',t.FILE_TYPE='" + FILE_TYPE + "',t.GATHERING_METHOD='" + GATHERING_METHOD + "'," +
	"t.SOURCE_SYSTEM='" + SOURCE_SYSTEM + "',t.DELIVERY_STATE='" + DELIVERY_STATE + "',t.SECURITY_LEVEL='" + SECURITY_LEVEL + "'," +
	"t.FILEPATH='" + FILEPATH + "',t.FILENAME='" + FILENAME + "',t.STATE_CHECK='" + STATE_CHECK + "',t.PRODUCT_ID='" + productId + "'," +
	"t.STATUS='" + STATUS + "',t.FILE_FORMAT='" + FILE_FORMAT + "',t.DOC_INFO='" + docInfo + "' " +
	"when not matched then insert (ID,NODECODE,NODENAME,FILE_NUMBER,FILE_NAME,FILE_TYPE,GATHERING_METHOD,SOURCE_SYSTEM,DELIVERY_STATE,SECURITY_LEVEL,FILEPATH,FILENAME,STATE_CHECK,PRODUCT_ID,STATUS,FILE_FORMAT,DOC_INFO,CREATE_TIMESTAMP,CREATOR) " +
	"values (" + newId + ",'" + NODECODE + "','" + NODENAME + "','" + FILE_NUMBER + "','" + FILE_NAME + "','" + FILE_TYPE + "','" + GATHERING_METHOD + "','" + SOURCE_SYSTEM + "','" + DELIVERY_STATE + "','" + SECURITY_LEVEL + "','" + FILEPATH + "','" + FILENAME + "','" + STATE_CHECK + "','" + productId + "','" + STATUS + "','" + FILE_FORMAT + "','" + docInfo + "','" + nowStr + "','wanghq')";

Things['Thing.DB.Oracle'].RunCommand({ sql: sql });
result = newId;


