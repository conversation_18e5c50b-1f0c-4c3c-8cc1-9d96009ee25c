/**
 * @definition    postGetProcessListInfoV2
 * @description   调用GetProcessListInfo接口并返回<Response>片段  wanghq
 * @implementation    {Script}
 *
 * @param    {STRING}    reqUrl
 * @param    {NUMBER}    processCode
 * @param    {STRING}    category
 * @param    {STRING}    entype
 *
 * @returns    {STRING}
 */
var content =
	'<?xml version="1.0" encoding="utf-8"?>' +
	'<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">' +
	  '<soap:Body>' +
		'<GetProcessListInfo xmlns="http://tempuri.org/">' +
		  '<xmlSend>&lt;Request&gt;&lt;ProcessCode&gt;' + processCode + '&lt;/ProcessCode&gt;&lt;Category&gt;' + category + '&lt;/Category&gt;&lt;Type&gt;' + entype + '&lt;/Type&gt;&lt;/Request&gt;</xmlSend>' +
		'</GetProcessListInfo>' +
	  '</soap:Body>' +
	'</soap:Envelope>';

var params = {
	headers: { "Content-Type": "text/xml; charset=utf-8" },
	url: reqUrl,
	timeout: 120000,
	content: content
};
var resultXml = Resources["ContentLoaderFunctions"].PostXML(params);
var contentXml = resultXml.*::Body.*::GetProcessListInfoResponse;
var rs = String(contentXml.*::GetProcessListInfoResult);
if (rs == null || rs === "") {
	result = "";
} else {
	var i1 = rs.indexOf("<Response");
	var i2 = rs.indexOf("</Response>");
	if (i1 >= 0 && i2 > i1) {
		result = rs.substring(i1, i2 + 11);
	} else {
		result = "";
	}
}


