/**
 * @definition    photoExtWhitelistV2
 * @description   Photo类型允许的扩展名（统一大小写） wanghq
 * @implementation    {Script}
 *
 * @param    {STRING}    ext
 *
 * @returns    {BOOLEAN}
 */
var ok = false;
var e = ext ? String(ext).toLowerCase() : "";
if (e == 'jpg' || e == 'png' || e == 'gif' || e == 'bmp' || e == 'mp4' || e == 'rmvb' || e == 'flv' || e == 'mov' || e == 'avi') {
	ok = true;
}
result = ok ? 'true' : 'false';


