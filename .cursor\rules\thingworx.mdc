---
globs: twx/**/**.js
alwaysApply: false
---

**项目结构约定**

本项目中，所有 ThingWorx 服务脚本均作为独立文件进行管理。每个服务对应一个 `.js` 文件，其存放路径遵循以下格式：

`twx/[ThingName]/[ServiceName].js`

# ThingWorx 服务开发核心规范

## 1. 核心开发原则

### 1.1. ES5 语法约束
ThingWorx 服务脚本**仅支持 ES5 语法**。严禁使用 ES6+ 特性。

-   **必须使用**:
    -   `var` 声明变量。
    -   `function` 关键字声明函数。
    -   `+` 号进行字符串拼接。
    -   传统 `for` 循环或 `for...in`。
-   **严禁使用**:
    -   `let`, `const`
    -   箭头函数 (`=>`)
    -   模板字符串 (`` `${}` ``)
    -   解构赋值、展开运算符 (`...`)
    -   `Promise`, `async/await`
    -   `class`

### 1.2. 唯一的出口：`result` 变量
所有服务脚本**必须**通过将最终返回值赋给 `result` 变量来结束。

-   **严禁**在脚本中间使用 `return` 语句提前退出。
-   若需提前中止，必须使用 `throw "..."` 抛出异常，由 `catch` 块统一处理。

### 1.3. JSDoc 注释标准
每个服务都必须包含标准 JSDoc 注释，以便于理解和维护。

```javascript
/**
 * @definition    [服务名称]    {"category":"ext"}
 * @description   [服务功能描述]  wanghq [创建/修改日期，格式：YYYY年MM月DD日HH:mm:ss]
 * @implementation    {Script}
 *
 * @param    {[类型]}    [参数名]    [参数描述]
 *
 * @returns    {[返回类型]}
 */
```
**说明**:
-   `@description` 包含功能、作者和时间（例如：`2025年6月9日14:30:25`）。

### 1.4. 命名规范
-   **服务名**: PascalCase (e.g., `GetUserList`)
-   **变量/参数**: camelCase (e.g., `userName`, `pageNumber`)

## 2. 标准响应与错误处理

### 2.1. 标准 JSON 响应结构
操作类服务推荐返回统一的 JSON 结构，便于前端处理。

```javascript
// 成功响应
{
    "success": true,
    "msg": "操作成功",
    "data": { ... } // 可选，承载返回数据
}

// 失败响应
{
    "success": false,
    "msg": "[服务名]-操作失败，原因：..."
}
```

### 2.2. 错误处理与日志记录
复杂的服务逻辑必须使用 `try-catch` 结构，并规范记录错误日志。

```javascript
/**
 * @definition    AddUser    {"category":"ext"}
 * @description   添加新用户  wanghq 2025年6月9日14:30:25
 * @implementation    {Script}
 * 
 * @param    {STRING}    userName    用户名
 * 
 * @returns    {JSON}
 */
var res = {}; // 初始化响应对象
try {
    // 1. 业务逻辑...
    if (userName === null || userName === "") {
        throw "用户名不能为空";
    }

    // 2. 数据库操作...
    
    // 3. 构造成功响应
    res.success = true;
    res.msg = "添加成功";

} catch (error) {
    // 4. 捕获异常并处理
    res.success = false;
    // 构造带服务名前缀的错误消息
    var msg = "AddUser-添加失败，原因：" + error; 
    logger.error(msg); // 记录到错误日志
    res.msg = msg; // 将错误信息返回给调用方
}

// 5. 将最终结果赋给 result
result = res;
```
**日志规范**:
-   所有日志输出（`logger.info`, `logger.error`, `logger.warn`, `logger.debug` 等）必须以服务名作为前缀，便于追踪。
-   错误日志应包含服务名、错误原因及关键上下文信息。

## 3. 数据库操作 (Oracle 11g)

### 3.1. SQL 注入防护
**严禁**将未经处理的用户输入直接拼接到 SQL 语句中。必须对字符串参数进行转义。

```javascript
// SQL安全转义函数（建议在公共Thing中实现）
function sqlEscape(value) {
    if (value === null || value === undefined) { return ""; }
    return String(value).replace(/'/g, "''");
}

// 正确示例
var userInput = "O'Malley";
var sql = "SELECT * FROM users WHERE last_name = '" + sqlEscape(userInput) + "'";
```

### 3.2. 数据查询与更新

```javascript
// 执行查询
var queryResult = Things['Thing.DB.Oracle'].RunQuery({sql: sql});

// 检查查询结果并处理
if (queryResult.rows.length > 0) {
    // 推荐直接遍历 rows，性能更优
    for (var i = 0; i < queryResult.rows.length; i++) {
        var row = queryResult.rows[i];
        // 注意：Oracle返回的字段名通常为大写，如 row.USERNAME
    }
    // 若需完整结构，再转换为JSON
    var jsonData = queryResult.ToJSON().rows;
}

// 执行更新、插入、删除
Things['Thing.DB.Oracle'].RunCommand({sql: sql});
```

### 3.3. 分页查询标准模式
分页查询应同时返回总记录数和当前页数据，采用标准响应结构。

```javascript
/**
 * @definition    QueryUserList    {"category":"ext"}
 * @description   分页查询用户列表  wanghq 2025年6月9日14:30:25
 * @implementation    {Script}
 * 
 * @param    {JSON}    params    查询参数, e.g., {"userName":"test", "pageNumber":1, "pageSize":10}
 * 
 * @returns    {JSON}
 */
var res = {
    code: 0,
    msg: "查询成功",
    count: 0,
    data: []
};

try {
    var pageNumber = params.pageNumber || 1;
    var pageSize = (params.pageSize > 0 && params.pageSize <= 1000) ? params.pageSize : 20; // 设置默认值和上限

    // 1. 构建查询条件
    var conditionSql = "";
    if (params.userName && params.userName !== "") {
        conditionSql += " AND user_name LIKE '%" + sqlEscape(params.userName) + "%'";
    }

    // 2. 查询总数
    var countSql = "SELECT COUNT(1) AS CNT FROM users WHERE 1=1" + conditionSql;
    var countResult = Things['Thing.DB.Oracle'].RunQuery({sql: countSql});
    if (countResult.rows.length > 0) {
        res.count = countResult.rows[0].CNT;
    }

    // 3. 查询分页数据
    if (res.count > 0) {
        var startRowno = (pageNumber - 1) * pageSize + 1;
        var endRowno = pageNumber * pageSize;
        
        var querySql = "SELECT * FROM (SELECT ROWNUM AS rowno, a.* FROM users a WHERE 1=1" +
                     conditionSql + " ORDER BY a.create_time DESC) s " +
                     "WHERE s.rowno >= " + startRowno + " AND s.rowno <= " + endRowno;
                     
        var dataResult = Things['Thing.DB.Oracle'].RunQuery({sql: querySql});
        res.data = dataResult.ToJSON().rows; // 此处可按需处理空值
    }

} catch (error) {
    res.code = 500;
    var msg = "QueryUserList-查询失败，原因：" + error.message;
    logger.error(msg);
    res.msg = msg;
}
result = res;
```

## 4. 服务间调用规范

### 4.1. 调用同一 Thing 内的服务
使用 `me.服务名()` 调用同一 Thing 内的其他服务。

```javascript
// 基本调用
var serviceResult = me.SomeOtherService({
    parameter1: value1 /* STRING */, // 参数传递时需要添加类型注释
    parameter2: value2 /* INTEGER */
});

// 示例：获取表格名称和条件SQL
var tableName = me.getDataSearchTableName({
    tableType: tableType /* STRING */
});
```
**说明**：参数变量名可以直接使用，ThingWorx 会自动传递参数值。

### 4.2. 调用其他 Thing 的服务
使用 `Things['Thing名称'].服务名()` 的格式来调用其他 Thing 上的服务。

```javascript
// 示例：调用数据库 Thing 执行 SQL 查询
var queryResult = Things['Thing.DB.Oracle'].RunQuery({
    sql: querySql /* STRING */
});
```

## 5. 常用 API 和函数

本节列举 ThingWorx 脚本中常用的 API 调用和辅助函数。

### 5.1. 时间处理
```javascript
// 获取当前时间并格式化
var nowTime = dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss");
```

### 5.2. 字符串处理
```javascript
// Base64编码（常用于密码等敏感信息处理）
var encodedString = base64EncodeString(originalString);
```
## 6. 外部 API 调用

统一使用 `Resources["ContentLoaderFunctions"]` 调用外部 HTTP 接口，并进行完整的错误处理和响应校验。

```javascript
/**
 * @definition    GetExternalData    {"category":"ext"}
 * @description   调用外部API获取数据  wanghq 2025年6月9日14:30:25
 * @implementation    {Script}
 * 
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: null };

try {
    var apiUrl = me.getProperty("ExternalApiUrl"); // URL从配置中读取，避免硬编码
    var token = me.getProperty("ApiToken"); // 凭证使用Password类型属性存储
    
    var options = {
        url: apiUrl,
        headers: {
            "Accept": "application/json",
            "Authorization": "Bearer " + token
        },
        timeout: 10000 // 设置超时时间（毫秒）
    };
    
    var response = Resources["ContentLoaderFunctions"].GetJSON(options);

    // 校验响应是否有效
    if (response === null || typeof response !== "object") {
        throw "API返回无效或空响应";
    }

    res.success = true;
    res.data = response;

} catch (error) {
    res.success = false;
    var msg = "GetExternalData-API调用失败：" + error;
    logger.error(msg);
    res.msg = "调用外部服务失败，请稍后重试"; // 返回对用户友好的信息
}
result = res;
```

## 7. 性能与并发

### 7.1. 避免 N+1 查询
严禁在循环中执行数据库或API调用。应先批量获取所有需要的数据，然后在内存中进行处理。

```javascript
// 反例: 循环查询
for (var i = 0; i < userIds.length; i++) {
    // 每次循环都访问数据库，性能极差
    var user = Things['Thing.DB.Oracle'].RunQuery({sql: "SELECT * FROM users WHERE id = " + userIds[i]});
}

// 正例: 批量查询
var idList = "('" + userIds.join("','") + "')"; // 简单示例，实际应做SQL转义
var sql = "SELECT * FROM users WHERE id IN " + idList;
var usersResult = Things['Thing.DB.Oracle'].RunQuery({sql: sql});
// 在内存中处理 usersResult
```

### 7.2. 长耗时任务
执行时间超过数秒的服务应设计为异步任务。前端发起任务后，通过轮询服务查询任务状态，避免请求超时。

### 7.3. 并发控制
对于可能被并发调用的写操作服务，应设计幂等性逻辑（多次调用结果一致），或使用数据库锁、版本号（乐观锁）等机制防止数据冲突。

## 8. 安全规范

1.  **输入验证**: 验证所有输入参数，防止不合法数据。
2.  **SQL注入防护**: 始终对用户输入进行转义（参考 3.1 节），对排序字段等进行白名单校验。
3.  **错误消息控制**: 返回给前端的错误信息应简洁友好，避免暴露详细的堆栈跟踪、SQL 语句或内部结构。详细错误记录在服务端日志中。

## 9. 附录

### 9.1. ThingWorx 参数类型说明

| 类型        | 描述             | 示例                    |
|-------------|------------------|-------------------------|
| `BLOB`      | 二进制大对象     | 文件数据                |
| `BOOLEAN`   | 布尔值           | `true`, `false`         |
| `DATETIME`  | 日期时间值       | `2025-06-09 14:30:25`   |
| `GUID`      | 全局唯一标识符   | `UUID`字符串            |
| `HTML`      | HTML格式内容     | `<h1>标题</h1>`         |
| `HYPERLINK` | URL链接          | `http://example.com`    |
| `IMAGE`     | 图像二进制数据   | 图片文件                |
| `INFOTABLE` | 数据表对象       | ThingWorx标准数据负载   |
| `INTEGER`   | 整数值           | `123`                   |
| `LONG`      | 长整数值         | `123456789`             |
| `JSON`      | JSON对象         | `{"key": "value"}`      |
| `LOCATION`  | WGS84坐标        | `[经度,纬度,海拔]`      |
| `NUMBER`    | 数值             | `123.45`                |
| `PASSWORD`  | 掩码密码值       | 加密密码                |
| `QUERY`     | 查询过滤器对象   | 包含过滤条件的JSON      |
| `STRING`    | 字符串           | `"文本内容"`            |
| `TEXT`      | 可搜索文本       | 长文本内容              |
| `THINGNAME` | Thing名称        | Thing的标识符           |
| `USERNAME`  | 用户名           | 系统用户标识            |
| `XML`       | XML文档          | `<root></root>`         |

### 9.2. 常见陷阱与反模式

-   **使用 `return`**: 导致服务行为不可预测。
-   **忽略 `result` 赋值**: 服务将无返回值。
-   **直接拼接SQL**: 存在严重的安全注入风险。
-   **循环中IO操作**: 导致严重的性能瓶颈（N+1问题）。
-   **不处理API或DB空响应**: 访问 `null` 或 `undefined` 的属性导致运行时错误。
-   **硬编码配置**: URL、用户名、密码等应通过Thing属性或配置表管理。
-   **在日志中打印敏感信息**: 造成安全信息泄露。
-   **盲目信任 Oracle 字段名大小写**: Oracle 数据库返回的字段名通常为大写，注意代码访问时的大小写匹配。