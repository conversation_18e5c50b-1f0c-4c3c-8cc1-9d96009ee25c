/**
 * @definition    insertXmlDataV2
 * @description   统一XML业务数据入库（各 entype） wanghq
 * @implementation    {Script}
 *
 * @param    {STRING}    type
 * @param    {XML}       tag
 * @param    {STRING}    source_id
 * @param    {NUMBER}    resultId
 * @param    {STRING}    excelFileName
 * @param    {STRING}    excelFilePath
 * @param    {STRING}    pdfFileName
 * @param    {STRING}    pdfFilePath
 * @param    {STRING}    fileName
 * @param    {STRING}    filePath
 * @param    {NUMBER}    pdf_resultId
 * @param    {NUMBER}    excel_resultId
 * @param    {STRING}    productId
 *
 * @returns    {NOTHING}
 */

// 直接复用旧版 insertXmlData 的 SQL 结构，保持表结构兼容
Things["Thing.Integration.DataCollect"].insertXmlData({
	type: type,
	tag: tag,
	source_id: source_id,
	resultId: resultId,
	excelFileName: excelFileName,
	excelFilePath: excelFilePath,
	pdfFileName: pdfFileName,
	pdfFilePath: pdfFilePath,
	fileName: fileName,
	filePath: filePath,
	pdf_resultId: pdf_resultId,
	excel_resultId: excel_resultId,
	productId: productId
});


