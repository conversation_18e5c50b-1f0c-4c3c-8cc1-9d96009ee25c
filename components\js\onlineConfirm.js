$(function () {
	loadTree();
	initTreeBtn();
});

layui.use(['layer', 'form', 'upload', 'table', 'laydate', 'dropdown'], function () {
	table = layui.table;
	layer = layui.layer;
	upload = layui.upload;
	form = layui.form;
	laydate = layui.laydate;
	dropdown = layui.dropdown;
	device = layui.device();
});

// 引入AIT树公共模块
// 注意：确保在HTML中已引入 aitTree.js 文件
var contextEle; //签名td元素
function reloadTable(treeNode, scrollObj) {
	var type = treeNode.TYPE;
	if (type == 'a' || type == 'b') {
		$("#msg").hide();
		$("#tbr").show();
		$("#table").show();
		initTbr(treeNode);
		var loadIndex = layer.load();
		var cb_success = function (res) {
			HotUtil.loadHtmlTable(res, treeNode, $("#table"), 85, funcIdent, scrollObj);
			layer.close(loadIndex);
		};
		var cb_error = function (xhr) {
			layer.close(loadIndex);
			layer.alert('加载表格失败!', {
				icon: 2
			});
		};
		twxAjax(THING, "QueryNodeById", {
			id: treeNode.ID
		}, true, cb_success, cb_error);

	} else {
		$("#msg").text("请选择A表或者B表！").show();
		$("#tbr").hide();
		$("#table").hide();
	}
}

function initTbr(treeNode) {
	if (treeNode.TABLE_STATUS == 'edit') {
		$('#eidt-table').show();
		$('#import-excel').show();
		$('#clear-sign').hide();
		if (treeNode.HTML_DATA) {
			$('#export-img').show();
			$('#export-pdf').show();
			$('#export-excel').show();
			$("#table-header").show();
			$('#confirm-table').show();
		} else {
			$('#export-img').hide();
			$('#confirm-table').hide();
			$("#table-header").hide();
			$('#export-pdf').hide();
			$('#export-excel').hide();
		}

		if (treeNode.TYPE == 'b') {
			$('#import-pdf').show();
			if (HotUtil.isPdf(treeNode)) {
				$('#import-excel').hide();
				$('#eidt-table').hide();
				$('#export-img').hide();
				$('#confirm-table').hide();
				$("#table-header").hide();
				$('#export-pdf').show();
				$('#export-excel').hide();
			}
		} else {
			$('#import-pdf').hide();
		}
	} else if (treeNode.TABLE_STATUS == 'sign') {
		$('#export-img').show();
		$("#table-header").show();
		$('#eidt-table').hide();
		$('#import-excel').hide();
		$('#confirm-table').hide();
		$('#clear-sign').show();
		$('#export-pdf').show();
		$('#export-excel').show();
		$('#import-pdf').hide();
	}


	//导入Excel
	$('#import-excel').unbind("click").bind('click', function () {
		var url = fileHandlerUrl + '/online/import/excel';
		HotUtil.openEdit(treeNode, 0, function () {
			HotUtil.importExcel(treeNode, url);
		});
	});

	//导出excel
	$('#export-excel').unbind("click").bind('click', function () {
		var url = fileHandlerUrl + "/online/export/excel";
		HotUtil.exportExcel(treeNode, url);
	});

	//下载所有照片
	$('#export-img').unbind("click").bind('click', function () {
		var url = fileHandlerUrl + "/online/export/img";
		HotUtil.exportImg(treeNode, url);
	});

	//导入PDF
	$('#import-pdf').unbind("click").bind('click', function () {
		var url = fileHandlerUrl + '/online/import/pdf';
		HotUtil.openEdit(treeNode, 0, function () {
			HotUtil.importPdf(treeNode, url);
		});
	});


	//导出PDF
	$('#export-pdf').unbind("click").bind('click', function () {
		if (HotUtil.isPdf(treeNode)) {
			var fileName = treeNode.TABLE_NUM + '：' + treeNode.NAME;
			downloadFile(treeNode.FILE_PATH, fileName + "." + treeNode.FILE_FORMAT);
		} else {
			// 使用PdfExportDialog模块显示导出设置弹窗
			PdfExportDialog.showDialog(function (options) {
				var url = fileHandlerUrl + "/online/export/pdf";

				// 导出PDF
				var loading;

				// 使用fileDownload处理文件下载
				$.fileDownload(url, {
					httpMethod: 'POST',
					data: {
						id: treeNode.ID,
						thing: THING,
						// 直接传递options对象属性，Spring会自动绑定到PdfOptions对象
						pageSize: options.pageSize,
						pageOrientation: options.pageOrientation
					},
					prepareCallback: function () {
						loading = layer.msg("正在导出PDF，请稍候...", {
							icon: 16,
							shade: 0.3,
							time: 0
						});
					},
					successCallback: function () {
						layer.close(loading);
					},
					failCallback: function (html, url) {
						layer.close(loading);
						layer.alert("导出失败", {
							icon: 2
						});
					}
				});
			});
		}
	});

	//设置表格表头行
	$('#table-header').unbind("click").bind('click', function () {
		HotUtil.updateHeaderRow(treeNode);
	});
	//编辑表格
	$('#eidt-table').unbind("click").bind('click', function () {
		if (treeNode.TABLE_STATUS != 'edit') {
			layer.alert("表格已经确认不可编辑！");
		} else {
			HotUtil.openEdit(treeNode, 1, function () {
				HotUtil.editTable(treeNode);
			});
		}
	});

	//确认表格之后可以签署
	$('#confirm-table').unbind("click").bind('click', function () {
		HotUtil.openEdit(treeNode, 0, function () {
			HotUtil.lockTable(treeNode);
		});
	});

	//清除表格签名
	$('#clear-sign').unbind("click").bind('click', function () {
		HotUtil.clearLock(treeNode);
	});

	//推送签署
	$('#push-sign').unbind("click").bind('click', function () {
		SignPushDialog.show(treeNode.ID, funcIdent);
	});
}

//加载树结构
function loadTree() {
	var cb_success = function (res) {
		if (res.success) {
			var datas = res.data;
			if (datas.length > 0) {
				datas = dealDataIcons(datas);
				datas = dealDataNodeName(datas);

				treeSetting.callback.onExpand = function (event, treeId, treeNode) {
					loadTreeMenu();
				};

				treeSetting.callback.onClick = function (event, treeId, treeNode) {
					reloadTable(treeNode);
				};
				ztreeObj = $.fn.zTree.init($("#" + treeId), treeSetting, datas);
				var node = ztreeObj.getNodeByParam("LEVEL_NUM", 0, null);
				ztreeObj.expandNode(node, true, false, true);
				loadTreeMenu();
				locateNode();
			}
		} else {
			layer.alert(res.msg);
		}
	};
	//使用ajax进行异步加载Tree
	twxAjax(THING, 'QueryTreeRoot', '', true, cb_success);
}


//操作完节点之后重新加载节点
function reloadTree(refrushId, selId) {
	if (selId) {

	} else {
		selId = refrushId;
	}
	var refrushTreeNode = ztreeObj.getNodeByParam("ID", refrushId, null);
	if (!refrushTreeNode.ISPARENT) {
		refrushTreeNode.ISPARENT = true;
		ztreeObj.updateNode(refrushTreeNode);
	}
	ztreeObj.reAsyncChildNodes(refrushTreeNode, 'refresh', false, function () {
		ztreeObj.expandNode(refrushTreeNode, true, false, true);
		var newSelNode = ztreeObj.getNodeByParam("ID", selId, null);
		ztreeObj.selectNode(newSelNode, false, true);
		loadTreeMenu();
		reloadTable(newSelNode);
	});
}

function initTreeBtn() {
	$('#expandAll').unbind("click").bind('click', function () {
		ztreeObj.expandAll(true);
		loadTreeMenu();
	})

	$('#collapseAll').unbind("click").bind('click', function () {
		ztreeObj.expandAll(false);
		var node = ztreeObj.getNodeByParam("LEVEL_NUM", 0, null);
		ztreeObj.expandNode(node, true, false, true);
		loadTreeMenu();
	})
}

//加载树节点右键菜单
function loadTreeMenu() {
	$("#dpTree a").each(function (i, n) {
		var node = ztreeObj.getNodeByTId($(n).parent().attr("id"));
		menu = getNodeMenu(node);
		if (menu.length != 0) {
			$(n).contextMenu({
				width: 140,
				menu: menu,
				target: function (ele) {
					var node = ztreeObj.getNodeByTId($(ele).parent().attr("id"));
					ztreeObj.selectNode(node, false, true);
					if (HotUtil.currentTreeNodeId !== node['ID']) {
						reloadTable(node);
					}
				}
			});
		}
	});
}

function selectWorkType(treeNode) {
	var addTpl = '<form class="layui-form" action="" lay-filter="selectWorkTypeForm">\
						<div class="layui-form-item">\
							<label class="layui-form-label">节点名称:</label>\
							<div class="layui-input-block">\
								<input type="text" readOnly="readOnly" value="' + treeNode['TABLE_NUM'] + ':' + treeNode['NAME'] + '" class="layui-input">\
							</div>\
						</div>\
						<div class="layui-form-item" style="">\
							<label class="layui-form-label">工作类型:</label>\
							<div class="layui-input-block">\
								<select name="workType" id="workType" lay-filter="workType">\
									<option value=""></option>\
								</select>\
							</div>\
						</div>\
						<div class="layui-form-item" style="display:none;">\
							<div class="layui-input-block">\
								<div class="layui-footer">\
									<button class="layui-btn" id="selectWorkTypeSubmit" lay-submit="" lay-filter="selectWorkTypeSubmit">确定</button>\
								</div>\
							</div>\
						</div>\
					</form>';
	var log = {};
	log.operation = '选择工作类型';
	log.tablePid = treeNode['PID'];
	log.tableId = treeNode['ID'];

	var layerIndex = layer.open({
		title: '选择工作类型',
		type: 1,
		fixed: false,
		maxmin: false,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		resize: false,
		area: ['500px', '220px'],
		content: '<div id="selectWorkTypeContent" style="padding-top: 15px;padding-right: 15px;"></div>',
		btn: ['确定', '取消'],
		yes: function () {
			$('#selectWorkTypeSubmit').click();
		},
		btn2: function () {
			return true;
		},
		success: function () {
			$("#selectWorkTypeContent").append(addTpl);
			$("#selectWorkTypeContent").parent().css("overflow", "visible");
			var datas = HotUtil.workTypes;
			for (var i = 0; i < datas.length; i++) {
				var workType = datas[i]['NAME'];
				var selWorkType = "";
				if (treeNode['SAVE_DATA']) {
					selWorkType = JSON.parse(treeNode['SAVE_DATA']).workType;
				}
				if (workType === selWorkType) {
					$("#workType").append('<option selected value="' + workType + '">' + workType +
						'</option>');
				} else {
					$("#workType").append('<option value="' + workType + '">' + workType + '</option>');
				}
			}
		}
	});
	form.render(null, 'selectWorkTypeForm');
	form.on('submit(selectWorkTypeSubmit)',
		function (data) {
			var param = data.field;
			param.id = treeNode['ID'];
			param.thing = THING;
			var workType = $("#workType option:selected").text();
			log.content = "将节点【" + treeNode['NAME'] + "（" + treeNode['ID'] + "）】的工作类型调整为【" + workType + "】";
			twxAjax("Thing.Util.HandsonTable", 'SelectWorkType', param, true, function (res) {
				if (res.success) {
					reloadTree(treeNode['PID'], treeNode['ID']);
					layer.close(layerIndex);
					log.reqResult = 1;
					addConfirmLog(log);
				} else {
					log.reqResult = 0;
					addConfirmLog(log);
					layer.alert(res.msg, {
						icon: 2
					});
				}
			});
			return false;
		});
}

function workHours(treeNode) {
	var log = {};
	log.operation = "结构树统计工时";
	log.tablePid = treeNode['PID'];
	log.tableId = treeNode['ID'];
	log.content = "在节点【" + treeNode['NAME'] + "（" + treeNode['ID'] + "）】上统计工时";

	var loading;
	$.fileDownload(fileHandlerUrl + '/online/export/work/hours', {
		httpMethod: 'POST',
		data: {
			"id": treeNode['ID'],
			"name": treeNode['NAME'] + "工时统计",
			"thing": THING
		},
		prepareCallback: function () {
			loading = layer.msg("正在统计中...", {
				icon: 16,
				shade: 0.3,
				time: 0
			});
		},
		abortCallback: function () {
			log.reqResult = 0;
			addConfirmLog(log);
			layer.close(loading);
			layer.msg("导出异常！！");
		},
		successCallback: function () {
			log.reqResult = 1;
			addConfirmLog(log);
			layer.close(loading);
		},
		failCallback: function (responseHtml, url, error) {
			log.reqResult = 0;
			addConfirmLog(log);
			layer.close(loading);
			try {
				layer.msg(JSON.parse($(responseHtml)[0].innerText).msg);
			} catch (e) {
				layer.msg("导出失败！！");
			}

		}
	});
}


//获取节点右键菜单数组
function getNodeMenu(treeNode) {
	var imgSuffix = '../dataTree/images/';
	var type = treeNode.TYPE;
	var menu = [];

	// 添加同步AIT确认表菜单项（仅对分级承诺B表节点显示）
	if (type === 'b') {
		// 检查是否是分级承诺B表节点
		if (isCommitmentBNode(treeNode)) {
			var syncAitConfirmMenu = {
				text: "同步AIT确认表",
				icon: imgSuffix + 'sync.png',
				callback: function () {
					syncAitConfirmTable(treeNode);
				}
			};
			menu.push(syncAitConfirmMenu);
		}
	}

	var addFolderMenu = {
		text: "添加分类",
		icon: imgSuffix + 'add1.png',
		callback: function () {
			HotUtil.addTableNode(treeNode, 'folder');
		}
	};

	var selectWorkTypeMenu = {
		text: "设置工作类型",
		icon: imgSuffix + 'workType.png',
		callback: function () {
			selectWorkType(treeNode);
		}
	};

	var selectFolderMenu = {
		text: "选择分类",
		icon: imgSuffix + 'folder1.png',
		callback: function () {
			var addTpl = '<form class="layui-form" action="" lay-filter="selectFolderForm">\
						<div class="layui-form-item">\
							<label class="fieldlabel1 layui-form-label">型号名称:</label>\
							<div class="layui-input-block">\
								<input type="text" readOnly="readOnly" value="' + treeNode.NAME + '" class="layui-input">\
							</div>\
						</div>\
						<div class="layui-form-item" style="">\
							<label class="fieldlabel1 layui-form-label">分类名称:</label>\
							<div class="layui-input-block">\
								<select name="folder" id="folder" lay-filter="folder" lay-verify="required">\
									<option value=""></option>\
								</select>\
							</div>\
						</div>\
						<div class="layui-form-item" style="display:none;">\
							<div class="layui-input-block">\
								<div class="layui-footer">\
									<button class="layui-btn" id="selectFolderSubmit" lay-submit="" lay-filter="selectFolderSubmit">确定</button>\
								</div>\
							</div>\
						</div>\
					</form>';
			var log = {};
			log.operation = '选择分类';
			log.tablePid = treeNode.PID;
			log.tableId = treeNode.ID;

			layer.open({
				title: '选择分类',
				type: 1,
				fixed: false,
				maxmin: false,
				anim: false,
				openDuration: 200,
				isOutAnim: false,
				closeDuration: 200,
				shadeClose: false,
				resize: false,
				//不允许拉伸
				area: ['500px', '220px'],
				content: '<div id="selectFolderContent" style="padding-top: 15px;padding-right: 15px;"></div>',
				btn: ['确定', '取消'],
				yes: function () {
					$('#selectFolderSubmit').click();
				},
				btn2: function () {
					return true;
				},
				success: function () {
					$("#selectFolderContent").append(addTpl);
					$("#selectFolderContent").parent().css("overflow", "visible");
					twxAjax(THING, 'QueryFolder', {},
						false,
						function (res) {
							if (res.success) {
								var datas = res.data;
								for (var i = 0; i < datas.length; i++) {
									var folderName = datas[i]['NAME'];
									var folderId = datas[i]['ID'];
									if (folderName == treeNode.getParentNode().NAME) {
										$("#folder").append('<option selected value="' +
											folderId + '">' + folderName + '</option>');
									} else {
										$("#folder").append('<option value="' + folderId +
											'">' + folderName + '</option>');
									}
								}
							} else {
								layer.alert(res.msg, {
									icon: 2
								});
							}
						});
				}
			});
			form.render(null, 'selectFolderForm');
			form.on('submit(selectFolderSubmit)',
				function (data) {
					var param = data.field;
					var folderId = param.folder;
					param.id = treeNode.ID;
					param.folderId = folderId;
					var folderName = $("#folder option:selected").text();
					log.content = "将型号【" + treeNode.NAME + "（" + treeNode.ID + "）】的分类调整为【" + folderName +
						"（" + folderId + "）】";
					twxAjax(THING, 'SelectFolder', param, true,
						function (res) {
							if (res.success) {
								log.reqResult = 1;
								layer.closeAll();
								layer.msg(res.msg);
								var rootNode = ztreeObj.getNodesByParam("TYPE", "root", null)[0];
								ztreeObj.reAsyncChildNodes(rootNode, 'refresh', false,
									function () {
										ztreeObj.reAsyncChildNodes(ztreeObj.getNodeByParam("ID",
											folderId, null), 'refresh', false,
											function () {
												loadTreeMenu();
											});
									});
							} else {
								log.reqResult = 0;
								layer.alert(res.msg, {
									icon: 2
								});
							}
							addConfirmLog(log);
						})
					return false;
				});

		}
	}
	var addModelMenu = {
		text: "添加型号",
		icon: imgSuffix + 'add1.png',
		callback: function () {
			HotUtil.addTableNode(treeNode, 'model');
		}
	};

	var copyNodeMenu = {
		text: "复制节点",
		icon: imgSuffix + 'copy1.png',
		callback: function () {
			HotUtil.addTableNode(treeNode, treeNode.TYPE, true);
		}
	};

	var moveNodeMenu = {
		text: "移动节点",
		icon: imgSuffix + 'transfer.png',
		callback: function () {
			HotUtil.moveNode(THING, treeNode);
		}
	};


	var addProjectMenu = {
		text: "添加工作项目",
		icon: imgSuffix + 'add1.png',
		callback: function () {
			HotUtil.addTableNode(treeNode, 'project');
		}
	};

	var addATable = {
		text: "添加A表",
		icon: imgSuffix + 'add1.png',
		callback: function () {
			HotUtil.addTableNode(treeNode, 'a');
		}
	};

	var importATable = {
		text: "导入A表",
		icon: imgSuffix + 'excelupload1.png',
		callback: function () {
			HotUtil.importTableNode(treeNode, 'a');
		}
	};

	var bacthImportATable = {
		text: "批量导入A表",
		icon: imgSuffix + 'excelupload1.png',
		callback: function () {
			HotUtil.batchImportExcel(treeNode, 'a');
		}
	};


	var addBTable = {
		text: "添加B表",
		icon: imgSuffix + 'add1.png',
		callback: function () {
			HotUtil.addTableNode(treeNode, 'b');
		}
	};


	var importBTable = {
		text: "导入B表",
		icon: imgSuffix + 'excelupload1.png',
		callback: function () {
			HotUtil.importTableNode(treeNode, 'b');
		}
	};

	var bacthImportBTable = {
		text: "批量导入B表",
		icon: imgSuffix + 'excelupload1.png',
		callback: function () {
			HotUtil.batchImportExcel(treeNode, 'b');
		}
	};

	var editNodeMenu = {
		text: "编辑节点",
		icon: imgSuffix + 'edit1.png',
		callback: function () {
			HotUtil.editTableNode(treeNode, funcIdent);
		}
	};


	var deleteNodeMenu = {
		text: "删除节点",
		icon: imgSuffix + 'remove1.png',
		callback: function () {
			HotUtil.deleteTableNode(treeNode);
		}
	};

	var pdfNodeMenu = {
		text: "导出PDF",
		icon: imgSuffix + 'pdf1.png',
		callback: function () {
			var log = {};
			log.operation = "结构树导出PDF";
			log.tablePid = treeNode.PID;
			log.tableId = treeNode.ID;
			log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上导出PDF";

			// 使用PdfExportDialog模块显示导出设置弹窗
			PdfExportDialog.showDialog(function (options) {
				log.reqResult = 1;
				addConfirmLog(log);
				twxAjax(THING, 'ReqGenerateFile', {
					tableId: treeNode.ID,
					tablePId: treeNode.PID,
					exportType: 1,
					creator: sessionStorage.getItem("username"),
					pageSize: options.pageSize,
					pageOrientation: options.pageOrientation
				}, true, function (res) { }, function (xhr, textStatus, errorThrown) { });
				layer.alert("文件正在生成中，请稍后在下载列表中查看下载！");
			});
		}
	};

	var pdfNodeMenu1 = {
		text: "导出PDF",
		icon: imgSuffix + 'pdf1.png',
		callback: function () {
			var log = {};
			log.operation = "结构树导出PDF";
			log.tablePid = treeNode.PID;
			log.tableId = treeNode.ID;
			log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上导出PDF";
			var loading;
			var url = fileHandlerUrl + "/online/export/more/pdf";
			$.fileDownload(url, {
				httpMethod: 'POST',
				data: {
					"id": treeNode.ID,
					"pid": treeNode.PID,
					"thing": THING
				},
				prepareCallback: function (url) {
					loading = layer.msg("正在导出...", {
						icon: 16,
						shade: 0.3,
						time: 0
					});
				},
				abortCallback: function (url) {
					layer.close(loading);
					log.reqResult = 0;
					layer.msg("导出异常！！");
					addConfirmLog(log);
				},
				successCallback: function (url) {
					log.reqResult = 1;
					layer.close(loading);
					addConfirmLog(log);
				},
				failCallback: function (html, url) {
					log.reqResult = 0;
					addConfirmLog(log);
					layer.close(loading);
					layer.alert(html, {
						icon: 2
					})
				}
			});
		}
	};

	var pdfZipNodeMenu = {
		text: "导出PDF压缩包",
		icon: imgSuffix + 'morepdf.png',
		callback: function () {
			var log = {};
			log.operation = "导出PDF压缩包";
			log.tablePid = treeNode.PID;
			log.tableId = treeNode.ID;
			log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上导出PDF压缩包";

			// 使用PdfExportDialog模块显示导出设置弹窗
			PdfExportDialog.showDialog(function (options) {
				var loading;
				var url = fileHandlerUrl + '/online/export/pdf/zip';
				$.fileDownload(url, {
					httpMethod: 'POST',
					data: {
						"id": treeNode.ID,
						"name": treeNode.NAME,
						"thing": THING,
						// 传递PDF导出选项
						"pageSize": options.pageSize,
						"pageOrientation": options.pageOrientation
					},
					prepareCallback: function (url) {
						loading = layer.msg("正在导出...", {
							icon: 16,
							shade: 0.3,
							time: 0
						});
					},
					abortCallback: function (url) {
						log.reqResult = 0;
						addConfirmLog(log);
						layer.close(loading);
						layer.msg("导出异常！！");
					},
					successCallback: function (url) {
						log.reqResult = 1;
						addConfirmLog(log);
						layer.close(loading);
					},
					failCallback: function (html, url) {
						log.reqResult = 0;
						addConfirmLog(log);
						layer.close(loading);
						layer.alert(html, {
							icon: 2
						})
					}
				});
			});
		}
	};

	var excelNodeMenu = {
		text: "导出Excel",
		icon: imgSuffix + 'excel1.png',
		callback: function () {
			var log = {};
			log.operation = "结构树导出Excel";
			log.tablePid = treeNode.PID;
			log.tableId = treeNode.ID;
			log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上导出Excel";

			var loading;
			var url = fileHandlerUrl + '/online/export/more/excel';
			$.fileDownload(url, {
				httpMethod: 'POST',
				data: {
					"id": treeNode.ID,
					"pid": treeNode.PID,
					"thing": THING
				},
				prepareCallback: function (url) {
					loading = layer.msg("正在导出...", {
						icon: 16,
						shade: 0.3,
						time: 0
					});
				},
				abortCallback: function (url) {
					log.reqResult = 0;
					addConfirmLog(log);
					layer.close(loading);
					layer.msg("导出异常！！");
				},
				successCallback: function (url) {
					log.reqResult = 1;
					addConfirmLog(log);
					layer.close(loading);
				},
				failCallback: function (html, url) {
					log.reqResult = 0;
					addConfirmLog(log);
					layer.close(loading);
					layer.msg("导出失败！！");
				}
			});
		}
	};

	var workHourMenu = {
		text: "统计工时",
		icon: imgSuffix + 'statistics.png',
		callback: function () {
			workHours(treeNode);
		}
	};

	var exportZipMenu = {
		text: "导出数据包",
		icon: imgSuffix + 'export-data.png',
		callback: function () {
			var log = {};
			log.operation = "导出数据包";
			log.tablePid = treeNode.PID;
			log.tableId = treeNode.ID;
			log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上导出数据包";
			log.reqResult = 1;
			addConfirmLog(log);
			twxAjax(THING, 'ReqGenerateFile', {
				tableId: treeNode.ID,
				tablePId: treeNode.PID,
				exportType: 4,
				creator: sessionStorage.getItem("username")
			}, true, function (res) { }, function (xhr, textStatus, errorThrown) { });
			layer.alert("文件正在生成中，请稍后在下载列表中查看下载！");

		}
	};

	var exportZipMenu1 = {
		text: "导出数据包",
		icon: imgSuffix + 'export-data.png',
		callback: function () {
			var log = {};
			log.operation = "导出数据包";
			log.tablePid = treeNode.PID;
			log.tableId = treeNode.ID;
			log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上导出数据包";


			var loading;
			var url = fileHandlerUrl + '/online/export/zip';
			$.fileDownload(url, {
				httpMethod: 'POST',
				data: {
					"id": treeNode.ID,
					"name": treeNode.NAME,
					"thing": THING
				},
				prepareCallback: function (url) {
					loading = layer.msg("正在导出...", {
						icon: 16,
						shade: 0.3,
						time: 0
					});
				},
				abortCallback: function (url) {
					log.reqResult = 0;
					addConfirmLog(log);
					layer.close(loading);
					layer.msg("导出异常！！");
				},
				successCallback: function (url) {
					log.reqResult = 1;
					addConfirmLog(log);
					layer.close(loading);
				},
				failCallback: function (html, url) {
					log.reqResult = 0;
					addConfirmLog(log);
					layer.close(loading);
					layer.msg("导出失败！！");
				}
			});
		}
	};

	var updateDirectoryMenu = {
		text: "更新目录",
		icon: imgSuffix + 'refresh.png',
		callback: function () {
			updateDirectory(treeNode);
		}
	};

	var importZipMenu = {
		text: "导入数据包",
		icon: imgSuffix + 'import-data.png',
		callback: function () {
			HotUtil.importBigZip(treeNode, fileHandlerUrl + '/online/import/big/zip');
		}
	};

	var syncCommitmentDataMenu = {
		text: "同步数据",
		icon: imgSuffix + 'sync.png',
		callback: function () {
			syncCommitmentData(treeNode);
		}
	};
	var stopCommitmentSyncMenu = {
		text: "终止同步",
		icon: imgSuffix + 'stop-sync.png',
		callback: function () {
			stopCommitmentSync(treeNode);
		}
	};

	var recoverCommitmentSyncMenu = {
		text: "恢复同步",
		icon: imgSuffix + 'recover.png',
		callback: function () {
			recoverCommitmentSync(treeNode);
		}
	};

	var generateApocalypseMenu = {
		text: "生成问题启示录",
		icon: imgSuffix + 'word.png',
		callback: function () {
			generateApocalypse(treeNode);
		}
	};

	var syncQualityProblemDataMenu = {
		text: "同步质量问题",
		icon: imgSuffix + 'sync.png',
		callback: function () {
			syncQualityProblemData(treeNode);
		}
	};

	var updateBTableNumbersMenu = {
		text: "更新B表序号",
		icon: imgSuffix + 'refresh.png',
		callback: function () {
			HotUtil.updateBTableNumbers(treeNode);
		}
	};

	var exportTableListMenu = {
		text: "导出确认表列表",
		icon: imgSuffix + 'excel1.png',
		callback: function () {
			// 导出确认表列表
			var url = fileHandlerUrl + "/online/export/confirm/table/list?nodeName=" + treeNode.NAME + "&nodeId=" + treeNode.ID + "&thing=" + THING;
			$.fileDownload(url, {
				httpMethod: 'POST',
				prepareCallback: function (url) {
					loading = layer.msg("正在导出...", {
						icon: 16,
						shade: 0.3,
						time: 0
					});
				},
				abortCallback: function (url) {
					layer.close(loading);
					layer.msg("导出异常！！");
				},
				successCallback: function (url) {
					layer.close(loading);
				},
				failCallback: function (html, url) {
					layer.close(loading);
					layer.msg("导出失败！！");
				}
			});
		}
	};

	var type = treeNode.TYPE;

	var allfuns = sessionStorage.getItem('funcids');
	var funcArr = allfuns.split(',');

	if (type == 'root') {
		menu.push(addFolderMenu);
	}

	if (type == 'folder') {
		menu.push(addModelMenu);
		menu.push(editNodeMenu);
		if (contains(funcArr, 'func-' + funcIdent + '-delete-node')) {
			if (!treeNode.ISPARENT) {
				menu.push(deleteNodeMenu);
			}
		}
	}

	if (type == 'model') {
		menu.push(selectFolderMenu);
		menu.push(addProjectMenu);
		menu.push(editNodeMenu);
		if (contains(funcArr, 'func-' + funcIdent + '-delete-node')) {
			menu.push(deleteNodeMenu);
		}
		menu.push(copyNodeMenu);
		menu.push(pdfNodeMenu);
		menu.push(pdfZipNodeMenu);
		menu.push(excelNodeMenu);
		menu.push(workHourMenu);
		if (contains(funcArr, 'func-' + funcIdent + '-export-zip-node')) {
			menu.push(exportZipMenu);
		}
		if (contains(funcArr, 'func-' + funcIdent + '-import-zip-node')) {
			menu.push(importZipMenu);
		}

		if (treeNode.NAME == '现场质量隐患') {
			menu.push(syncQualityProblemDataMenu);
		}
	}
	if (type == 'project') {
		menu.push(addATable);
		menu.push(importATable);
		menu.push(bacthImportATable);
		menu.push(editNodeMenu);
		if (contains(funcArr, 'func-' + funcIdent + '-delete-node')) {
			menu.push(deleteNodeMenu);
		}
		menu.push(copyNodeMenu);
		menu.push(moveNodeMenu);
		menu.push(pdfNodeMenu);
		menu.push(pdfZipNodeMenu);
		menu.push(excelNodeMenu);
		menu.push(workHourMenu);
		if (contains(funcArr, 'func-' + funcIdent + '-export-zip-node')) {
			menu.push(exportZipMenu);
		}
		if (contains(funcArr, 'func-' + funcIdent + '-import-zip-node')) {
			menu.push(importZipMenu);
		}
		if (treeNode.getParentNode().NAME == '现场质量隐患') {
			menu.push(generateApocalypseMenu);
		}
	}
	if (type == 'a') {
		// 检查是否是分级承诺A表节点
		if (isCommitmentANode(treeNode) && funcIdent == 'launch') {
			if (treeNode.SYNC_STATUS == 'STOPPED') {
				// 恢复同步按钮需要权限控制
				if (contains(funcArr, 'func-' + funcIdent + '-recover-sync')) {
					menu.push(recoverCommitmentSyncMenu);
				}
			} else {
				menu.push(syncCommitmentDataMenu);
				menu.push(stopCommitmentSyncMenu);
			}

		}
		menu.push(addBTable);
		menu.push(importBTable);
		menu.push(bacthImportBTable);
		menu.push(updateBTableNumbersMenu);
		menu.push(exportTableListMenu);
		menu.push(editNodeMenu);
		if (contains(funcArr, 'func-' + funcIdent + '-delete-node')) {
			menu.push(deleteNodeMenu);
		}
		menu.push(copyNodeMenu);
		menu.push(moveNodeMenu);
		menu.push(pdfNodeMenu);
		menu.push(pdfZipNodeMenu);
		menu.push(excelNodeMenu);
		menu.push(workHourMenu);
		if (contains(funcArr, 'func-' + funcIdent + '-export-zip-node')) {
			menu.push(exportZipMenu);
		}
		if (contains(funcArr, 'func-' + funcIdent + '-import-zip-node')) {
			menu.push(importZipMenu);
		}
	}
	if (type == 'b') {
		menu.push(editNodeMenu);
		if (contains(funcArr, 'func-' + funcIdent + '-delete-node')) {
			menu.push(deleteNodeMenu);
		}
		menu.push(copyNodeMenu);
		menu.push(moveNodeMenu);
		menu.push(selectWorkTypeMenu);
		menu.push(workHourMenu);

		// 检查是否为目录表且父表是卫星产品履历书或卫星产品证明书
		if (treeNode.NAME === '目录') {
			var parentNode = ztreeObj.getNodeByTId(treeNode.parentTId);
			if (parentNode && parentNode.TYPE === 'a' &&
				(parentNode.NAME.includes('卫星产品履历书') ||
					parentNode.NAME.includes('卫星产品证明书'))) {
				menu.push(updateDirectoryMenu);
			}
		}

		if (contains(funcArr, 'func-' + funcIdent + '-export-zip-node')) {
			menu.push(exportZipMenu);
		}
		if (contains(funcArr, 'func-' + funcIdent + '-import-zip-node')) {
			menu.push(importZipMenu);
		}
	}
	return menu;
}

/**
 * 弹出下载列表
 */
function ejectDownloadTable() {
	var options = {};
	options.seachFormHtml = `<form class="layui-form search-form" lay-filter="download-table-form">
								<div class="layui-form-item">
									<div class="layui-inline">
										<label class="layui-form-label">分类</label>
										<div class="layui-input-inline">
											<select id="s_folder" name="s_folder" lay-filter="s_folder" lay-search></select>
										</div>
									</div>
									<div class="layui-inline">
										<label class="layui-form-label">型号</label>
										<div class="layui-input-inline">
											<select id="s_model" name="s_model" lay-filter="s_model" lay-search></select>
										</div>
									</div>
									<div class="layui-inline">
										<label class="layui-form-label">项目</label>
										<div class="layui-input-inline">
											<select id="s_project" name="s_project" lay-filter="s_project" lay-search></select>
										</div>
									</div>
									<div class="layui-inline">
										<label class="layui-form-label">A表</label>
										<div class="layui-input-inline">
											<input type="text" name="s_a" autocomplete="off" class="layui-input">
										</div>
									</div>
									<div class="layui-inline">
										<button class="layui-btn layui-btn-sm" lay-submit lay-filter="download-table-search">搜索</button>
										<button class="layui-btn layui-btn-sm layui-btn-primary" type="reset">重置</button>
									</div>
								</div>
							</form>`;
	options.cols = [{
		field: 'PROJECT',
		width: 200,
		title: '项目',
		templet: function (d) {
			return d.PROJECT == '-' ? "" : d.PROJECT;
		}
	}];
	options.tableHeight = 600;
	options.tableWidth = 1500;
	HotUtil.ejectDownloadTable(options);
}

function reloadSelect(sucessFn, s_folder = '', s_model = '', s_project = '') {
	twxAjax(THING, 'QueryDownloadSearch', {
		creator: sessionStorage.getItem("username"),
		s_folder: s_folder,
		s_model: s_model,
		s_project: s_project
	}, true, function (res) {
		if (res.success) {
			var folders = res.data.folders;
			var models = res.data.models;
			var projects = res.data.projects;
			if (s_folder == '') {
				$("#s_folder").empty().append('<option value=""></option>');
				for (var i = 0; i < folders.length; i++) {
					$("#s_folder").append('<option value="' + folders[i] + '">' + folders[i] + '</option>');
				}
			}

			if (s_model == '') {
				$("#s_model").empty().append('<option value=""></option>');
				for (var i = 0; i < models.length; i++) {
					$("#s_model").append('<option value="' + models[i] + '">' + models[i] + '</option>');
				}
			}

			if (s_project == '') {
				$("#s_project").empty().append('<option value=""></option>');
				for (var i = 0; i < projects.length; i++) {
					if ('-' !== projects[i]) {
						$("#s_project").append('<option value="' + projects[i] + '">' + projects[i] +
							'</option>');
					}
				}
			}
			form.render(null, 'download-table-form');
			sucessFn();
		} else {
			layer.alert(res.msg, {
				icon: 2
			});
		}
	}, function (xhr, textStatus, errorThrown) {
		layer.alert('请求出错！', {
			icon: 2
		});
	});
}

/**
 * 加载搜索表单
 */
function renderSearchForm() {
	reloadSelect(function () {
		form.on('select(s_folder)', function (data) {
			var value = data.value;
			reloadSelect(function () { }, value);
		});

		form.on('select(s_model)', function (data) {
			reloadSelect(function () { }, $("#s_folder").val(), data.value);
		});

		// 搜索提交
		form.on('submit(download-table-search)', function (data) {
			var field = data.field; // 获得表单字段
			field.creator = sessionStorage.getItem("username");
			// 执行搜索重载
			table.reload('download-table', {
				page: {
					curr: 1 // 重新从第 1 页开始
				},
				where: field // 搜索的字段
			});
			return false;
		});
	});
}

/**
 *
 */
function renderDownloadTable() {
	// 创建渲染实例
	table.render({
		elem: '#download-table',
		id: 'download-table',
		url: getUrl(THING, 'QueryDownloadTable'),
		where: {
			creator: sessionStorage.getItem("username")
		},
		height: 600, // 最大高度减去其他容器已占有的高度差
		cellMinWidth: 80,
		page: {
			layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip']
		},
		cols: [
			[{
				title: '序号',
				type: "numbers",
				width: 60
			},
			{
				field: 'DWONLOAD_ID',
				hide: true
			},
			{
				field: 'FOLDER',
				width: 120,
				title: '分类'
			},
			{
				field: 'MODEL',
				title: '型号',
				width: 120
			},
			{
				field: 'PROJECT',
				width: 200,
				title: '项目',
				templet: function (d) {
					return d.PROJECT == '-' ? "" : d.PROJECT;
				},
			},
			{
				field: 'A_TABLE',
				width: 300,
				title: 'A表',
				templet: function (d) {
					return d.A_TABLE == '：' ? "" : d.A_TABLE;
				},
			},
			{
				field: 'EXPORT_TYPE',
				title: '文件类型',
				width: 85,
				templet: function (d) {
					var html = "";
					if (d.EXPORT_TYPE == 1) {
						html = '<span class="layui-badge layui-bg-blue">Pdf</span>';
					} else if (d.EXPORT_TYPE == 2) {
						html = '<span class="layui-badge layui-bg-green">Pdf压缩包</span>';
					} else if (d.EXPORT_TYPE == 3) {
						html = '<span class="layui-badge layui-bg-red">Excel压缩包</span>';
					}
					return html;
				},
				align: 'center'
			},
			{
				field: 'START_TIME',
				width: 152,
				title: '提交时间',
				align: 'center'
			},
			{
				field: 'IS_COMPLETE',
				title: '是否完成',
				width: 90,
				minWidth: 90,
				templet: function (d) {
					var html = "";
					if (d.IS_COMPLETE == 0) {
						html = '<span class="layui-badge layui-bg-blue">进行中</span>';
					} else if (d.IS_COMPLETE == 1) {
						html = '<span class="layui-badge layui-bg-green">已完成</span>';
					} else if (d.IS_COMPLETE == 2) {
						html =
							'<span class="layui-badge layui-bg-red show-msg" title="点击查看原因" msg="' +
							d.MSG + '">生成失败</span>';
					}
					return html;
				},
				align: 'center'
			},
			{
				field: 'END_TIME',
				title: '完成时间',
				width: 152,
				align: 'center'
			},
			{
				field: 'IS_DOWNLOAD',
				title: '是否下载',
				width: 85,
				templet: function (d) {
					var html = "";
					if (d.IS_DOWNLOAD == 0) {
						html = '<span class="layui-badge layui-bg-blue">未下载</span>';
					} else if (d.IS_DOWNLOAD == 1) {
						html = '<span class="layui-badge layui-bg-green">已下载</span>';
					}
					return html;
				},
				align: 'center'
			},
			{
				fixed: 'right',
				title: '操作',
				width: 100,
				minWidth: 100,
				toolbar: `<div class="layui-clear-space">
								<a class="layui-btn layui-btn-xs" lay-event="download">下载</a>
							</div>`,
				align: 'left'
			}
			]
		],
		done: function () {
			$(".show-msg").off('click').on('click', function () {
				layer.alert($(this).attr("msg"));
			});
		},
		error: function (res, msg) {
			console.log(res, msg)
		}
	});

	// 工具栏事件
	table.on('tool(download-table)', function (obj) {
		var data = obj.data; // 获得当前行数据
		if (obj.event === 'download') {
			if (data.FILE_PATH) {
				var filePath = "//" + data.FILE_PATH;
				filePath = filePath.replace(/\\/g, "/");
				var fileName = data.FILE_NAME;
				twxAjax(THING, 'RecordDownloadFile', {
					downloadId: data.ID
				}, true, function (res) {
					downloadFile(filePath, fileName);
					table.reload('download-table');
				}, function () { });
			} else {
				layer.alert("文件还未生成，请稍后再试！", {
					icon: 2
				});
			}
		}
	});
}

// 检查是否是分级承诺A表节点
function isCommitmentANode(node) {
	if (node.TYPE !== 'a' || !node.NAME.includes('分级承诺')) {
		return false;
	}

	// 获取父节点（工作项目节点/型号）
	var parentNode = getParentNode(node);
	if (!parentNode) return false;

	// 获取祖父节点（型号节点）
	var grandParentNode = getParentNode(parentNode);
	if (!grandParentNode || !grandParentNode.NAME.includes('承诺书')) return false;

	return true;
}

// 检查是否是卫星产品履历书的A表节点
function isSatelliteProductAitNode(node) {
	if (node.TYPE !== 'a' || !node.NAME.includes('履历书')) {
		return false;
	}

	// 获取父节点（工作项目节点/型号）
	var parentNode = getParentNode(node);
	if (!parentNode) return false;

	// 获取祖父节点（型号节点）
	var grandParentNode = getParentNode(parentNode);
	if (!grandParentNode || !grandParentNode.NAME.includes('承诺书')) return false;

	return true;
}

// 检查是否是分级承诺或者卫星产品履历书的B表节点
function isCommitmentBNode(node) {
	if (node.TYPE !== 'b') {
		return false;
	}

	// 获取父节点（A表节点）
	var parentNode = getParentNode(node);
	if (!parentNode || (!isCommitmentANode(parentNode) && !isSatelliteProductAitNode(parentNode))) {
		return false;
	}

	return true;
}

// 获取父节点
function getParentNode(node) {
	var treeObj = $.fn.zTree.getZTreeObj("dpTree");
	return node ? treeObj.getNodeByTId(node.parentTId) : null;
}

/**
 * 更新目录表内容
 * @param {Object} treeNode 目录节点对象
 */
function updateDirectory(treeNode) {
	// 检查表格状态是否为锁定
	if (treeNode.TABLE_STATUS === 'sign') {
		layer.alert("目录表已锁定，无法更新", {
			icon: 2
		});
		return;
	}

	// 记录日志
	var log = {};
	log.operation = "更新目录";
	log.tablePid = treeNode.PID;
	log.tableId = treeNode.ID;
	log.content = "更新节点【" + treeNode.NAME + "（" + treeNode.ID + "）】的目录内容";

	// 使用PdfExportDialog模块显示目录更新设置弹窗
	PdfExportDialog.showDirectoryUpdateDialog(function (options) {
		// 显示进度提示
		var loading = layer.msg("正在生成目录并计算页码，请稍候...", {
			icon: 16,
			shade: 0.3,
			time: 0
		});

		// 调用后端服务更新目录并生成PDF
		twxAjax(THING, "UpdateDirectory", {
			nodeId: treeNode.ID,
			username: sessionStorage.getItem("username"),
			// 使用PdfOptions对象属性传递PDF页面设置
			pageSize: options.pageSize,
			pageOrientation: options.pageOrientation
		}, true, function (res) {
			if (res.success) {
				log.reqResult = 1;
				addConfirmLog(log);

				if (res.taskId) {
					// 如果返回了任务ID，说明是异步处理，需要轮询检查状态
					checkDirectoryUpdateStatus(res.taskId, treeNode, loading);
				} else {
					// 同步处理完成
					layer.close(loading);
					layer.msg(res.msg || '目录更新成功');
					reloadTable(treeNode);
				}
			} else {
				log.reqResult = 0;
				addConfirmLog(log);
				layer.close(loading);
				layer.alert(res.msg || '目录更新失败', {
					icon: 2
				});
			}
		}, function () {
			log.reqResult = 0;
			addConfirmLog(log);
			layer.close(loading);
			layer.alert('更新目录失败', {
				icon: 2
			});
		});
	});
}

/**
 * 检查目录更新状态
 * @param {String} taskId 任务ID
 * @param {Object} treeNode 目录节点对象
 * @param {Object} loading 加载提示对象
 */
function checkDirectoryUpdateStatus(taskId, treeNode, loading) {
	// 轮询间隔（毫秒）
	var pollInterval = 3000;

	// 创建轮询函数
	function pollStatus() {
		twxAjax(THING, "CheckDirectoryUpdateStatus", {
			taskId: taskId
		}, true, function (res) {
			if (res.success) {
				if (res.status === 'completed') {
					// 任务完成
					layer.close(loading);
					layer.msg('目录更新成功，页码已更新');
					reloadTable(treeNode);
				} else if (res.status === 'failed') {
					// 任务失败
					layer.close(loading);
					layer.alert(res.msg || '目录页码更新失败', {
						icon: 2
					});
					reloadTable(treeNode);
				} else {
					// 任务仍在进行中，继续轮询
					setTimeout(pollStatus, pollInterval);
				}
			} else {
				// 查询状态失败
				layer.close(loading);
				layer.alert(res.msg || '查询更新状态失败', {
					icon: 2
				});
				reloadTable(treeNode);
			}
		}, function () {
			// 请求失败
			layer.close(loading);
			layer.alert('查询更新状态失败', {
				icon: 2
			});
			reloadTable(treeNode);
		});
	}

	// 开始轮询
	setTimeout(pollStatus, pollInterval);
}

function locateNode() {
	var bpmTaskInfo = sessionStorage.getItem("bpmTaskInfo");
	if (bpmTaskInfo) {
		var taskInfo = JSON.parse(bpmTaskInfo);
		var tableId = taskInfo.TABLE_ID;
		twxAjax(THING, "QueryAllPNode", {
			nodeId: tableId
		}, true, function (res) {
			if (res.success) {
				locationConfirmTreeNode(res.data, function (thisNode) {
					$("#" + thisNode.tId + "_a").click();
				});
			}
		});
	}
}

function locationConfirmTreeNode(allDatas, callbackFn) {
	function recursionTree(allDatasIndex) {
		var treeId = allDatas[allDatasIndex]['ID'];
		if (treeId !== undefined) {
			var thisNode = ztreeObj.getNodeByParam('ID', treeId, null);
			if (allDatasIndex == allDatas.length - 1) {
				ztreeObj.selectNode(thisNode);
				callbackFn(thisNode);
			} else {
				//如果这个节点是父节点的话
				if (thisNode.ISPARENT) {
					//判断是否是展开的状态
					if (thisNode.open) {
						var newIndex = allDatasIndex + 1;
						recursionTree(newIndex);
					} else {
						//如果没有展开的话需要请求该节点下的子数据
						ztreeObj.reAsyncChildNodes(thisNode, "refresh", false, function () {
							loadTreeMenu();
							//展开之后再判断下一层级的节点
							var newIndex = allDatasIndex + 1;
							recursionTree(newIndex);
						});
					}
				}
			}
		}
	}
	recursionTree(0);
}