var treeId = 'dpTree';
var ztreeObj;
var curDragNodes, autoExpandNode;

// 引入AIT树公共模块
// 注意：确保在HTML中已引入 aitTree.js 文件
//tree setting
var treeSetting = {
    view: {
        dblClickExpand: false, //双击节点时，是否自动展开父节点的标识
        showLine: true, //是否显示节点之间的连线
        fontCss: {
            'color': 'black'
        }, //字体样式函数
        selectedMulti: false, //设置是否允许同时选中多个节点,
        txtSelectedEnable: true,
        showTitle: true,
        nameIsHTML: true
    },
    async: {
        enable: true,
        url: getTreeUrl(THING, "AsyncQueryTree", ""),
        type: "post",
        autoParam: ["ID"],
        contentType: "application/json;charset=utf-8",
        dataType: 'json',
        dataFilter: function (treeId, parentNode, res) {
            if (res.success) {
                var datas = res.data;
                if (datas.length > 0) {
                    // 调用AIT树公共模块中的方法
                    datas = dealAitDataIcons(datas);
                    datas = dealAitDataNodeName(datas);
                }
                return datas;
            } else {
                layer.alert(res.msg);
            }
        }
    },
    check: {
        //chkboxType: { "Y": "ps", "N": "ps" },
        chkboxType: {
            "Y": "",
            "N": ""
        },
        chkStyle: "checkbox", //复选框类型
        enable: false //每个节点上是否显示 CheckBox
    },
    edit: {
        enable: true,
        editNameSelectAll: false,
        showRemoveBtn: false,
        showRenameBtn: false,
        removeTitle: "删除",
        renameTitle: "重命名",
        drag: {
            autoExpandTrigger: true,
            prev: dropPrev,
            inner: dropInner,
            next: dropNext
        }
    },
    data: {
        simpleData: { //简单数据模式
            enable: true,
            idKey: "ID",
            pIdKey: "PID",
            rootPId: 1
        },
        key: {
            name: 'TEXT',
            title: '',
            isParent: "ISPARENT"
        }
    },
    callback: {
        beforeDrag: beforeDrag,
        beforeDrop: beforeDrop,
        beforeDragOpen: beforeDragOpen,
        onDrag: onDrag,
        onDrop: onDrop,
        beforeEditName: function (treeId, treeNode) {
            treeNode.NODENAME = getNodeName(treeNode.NODENAME);
            return true;
        },
        onRemove: function (event, treeId, treeNode) {
        }
    }
};

function dropPrev(treeId, nodes, targetNode) {
    var pNode = targetNode.getParentNode();
    if (pNode && pNode.dropInner === false) {
        return false;
    } else {
        for (var i = 0, l = curDragNodes.length; i < l; i++) {
            var curPNode = curDragNodes[i].getParentNode();
            if (curPNode && curPNode !== targetNode.getParentNode() && curPNode.childOuter === false) {
                return false;
            }
        }
    }
    return true;
}

function dropInner(treeId, nodes, targetNode) {
    if (targetNode && targetNode.dropInner === false) {
        return false;
    } else {
        for (var i = 0, l = curDragNodes.length; i < l; i++) {
            if (!targetNode && curDragNodes[i].dropRoot === false) {
                return false;
            } else if (curDragNodes[i].parentTId && curDragNodes[i].getParentNode() !== targetNode && curDragNodes[i].getParentNode()
                .childOuter === false) {
                return false;
            }
        }
    }
    return true;
}

function dropNext(treeId, nodes, targetNode) {
    var pNode = targetNode.getParentNode();
    if (pNode && pNode.dropInner === false) {
        return false;
    } else {
        for (var i = 0, l = curDragNodes.length; i < l; i++) {
            var curPNode = curDragNodes[i].getParentNode();
            if (curPNode && curPNode !== targetNode.getParentNode() && curPNode.childOuter === false) {
                return false;
            }
        }
    }
    return true;
}

function beforeDrag(treeId, treeNodes) {
    for (var i = 0, l = treeNodes.length; i < l; i++) {
        if (treeNodes[i].drag === false) {
            curDragNodes = null;
            return false;
        } else if (treeNodes[i].parentTId && treeNodes[i].getParentNode().childDrag === false) {
            curDragNodes = null;
            return false;
        }
    }
    curDragNodes = treeNodes;
    return true;
}

function beforeDragOpen(treeId, treeNode) {
    autoExpandNode = treeNode;
    return true;
}

function beforeDrop(treeId, treeNodes, targetNode, moveType, isCopy) {
    return true;
}

function onDrag(event, treeId, treeNodes) {
}


function onDrop(event, treeId, treeNodes, targetNode, moveType, isCopy) {
    if (targetNode != null) {
        var sourceNodeSort = treeNodes[0].SORT;
        var sourceNodeId = treeNodes[0].ID;
        var sourceNodePId = treeNodes[0].PID;
        var sourceNodeName = treeNodes[0].NAME;

        var targetNodeSort = targetNode.SORT;
        var targetNodeId = targetNode.ID;
        var targetNodeName = targetNode.NAME;
        var type = "上面";
        if (sourceNodeSort < targetNodeSort) {
            type = '下面';
        }
        var parentNode = treeNodes[0].getParentNode();
        var allNode = parentNode.children;
        var arr = [];
        for (var i = 1; i <= allNode.length; i++) {
            arr.push(allNode[i - 1].ID + ":" + i);
        }
        var str = arr.join(",");
        twxAjax(THING, 'DragNode', {
            str: str
        }, true, function (res) {
            if (res.success) {
                reloadTree(sourceNodePId, sourceNodeId);
            } else {
                layer.alert(res.msg);
            }
        }, function (data) {
            layer.alert("拖动失败！");
        });
    }
}


// 注意：dealDataIcons 方法已移至公共模块 aitTree.js 中
// 如需使用，请直接调用公共模块中的 dealAitDataIcons(datas) 方法

// 注意：dealDataNodeName 方法已移至公共模块 aitTree.js 中
// 如需使用，请直接调用公共模块中的 dealAitDataNodeName(datas) 方法

function getSelTreeNode() {
    var ztreeObj = $.fn.zTree.getZTreeObj("dpTree");
    var selNodes = ztreeObj.getSelectedNodes();
    var treeNode;
    if (selNodes.length > 0) {
        treeNode = selNodes[0];
    } else {
        treeNode = ztreeObj.getNodeByParam("TREEID", 1, null);
    }
    return treeNode;
}

//去除显示节点前的序号
function getNodeName(name) {
    if (name.indexOf("-") > -1) {
        var arr = name.split("-");
        var arr2 = [];
        for (var i = 1; i < arr.length; i++) {
            arr2.push(arr[i]);
        }
        return arr2.join("-")
    }
    return name;
}

//获取显示节点前的序号
function getNodeNum(name) {
    if (name.indexOf("-") > -1) {
        var arr = name.split("-");
        return arr[0] + "-";
    }
    return "";
}

function locationAitTreeNode(allDatas, callbackFn) {
    function recursionTree(allDatasIndex) {
        var treeId = allDatas[allDatasIndex]['ID'];
        if (treeId !== undefined) {
            var thisNode = ztreeObj.getNodeByParam('ID', treeId, null);
            if (allDatasIndex == allDatas.length - 1) {
                ztreeObj.selectNode(thisNode);
                callbackFn(thisNode);
            } else {
                //如果这个节点是父节点的话
                if (thisNode.ISPARENT) {
                    //判断是否是展开的状态
                    if (thisNode.open) {
                        var newIndex = allDatasIndex + 1;
                        recursionTree(newIndex);
                    } else {
                        //如果没有展开的话需要请求该节点下的子数据
                        ztreeObj.reAsyncChildNodes(thisNode, "refresh", false, function () {
                            // wanghq: 异步加载子节点完成后重新绑定右键菜单，确保所有层级节点的右键菜单功能正常
                            loadTreeMenu();
                            //展开之后再判断下一层级的节点
                            var newIndex = allDatasIndex + 1;
                            recursionTree(newIndex);
                        });
                    }
                }
            }
        }
    }
    recursionTree(0);
}