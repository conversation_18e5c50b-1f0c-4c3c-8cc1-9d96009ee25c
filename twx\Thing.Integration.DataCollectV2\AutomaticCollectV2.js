/**
 * @definition    AutomaticCollectV2
 * @description   自动采集V2入口（差异置旧、策略映射、缓存外提）  wanghq
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeid        {"aspect.defaultValue":"0.0"}
 *
 * @returns    {STRING}
 */
var startMs = new Date().getTime();
var rsLen = 0; // 业务记录计数
var resultRowCounter = 0; // 落表记录计数（估算）
var message = "";

try {
	var reqUrl = Things["Thing.Integration.DataCollectV2"].getReqUrlV2();
	if (reqUrl === '') {
		result = '未配置MES项目清单接口路径';
	} else {
		// 预取缓存
		var isStructuralAssembly = Things["Thing.Integration.DataCollectV2"].isStructuralAssemblyV2({ treeid: treeid });
		var fileUploadPath = Things["Thing.Fn.SystemDic"].getFileUploadPath();
		var model = Things["Thing.Integration.DataCollectV2"].getModelByTreeidV2({ treeid: treeid });

		// 查询待同步数据
		var sql = "select a.*,b.REFTREEID from alldatalistview a,DATA_PACKAGE b where a.NODECODE=b.ID and a.gathering_method='自动采集' and a.collectstatus='1'";
		if (treeid !== 0) {
			sql += " and b.REFTREEID=" + treeid;
		}
		var allWait = Things['Thing.DB.Oracle'].RunQuery({ sql: sql });
		for (var x = 0; x < allWait.rows.length; x++) {
			var row = allWait.rows[x];
			var processCode = row.REFTREEID;
			var type = row.FILE_TYPE;
			var category = row.TABLETYPE; // 设计/工艺/质量综合/过程控制
			var tableName = '';
			if (category == "设计") {
				tableName = "DESIGN_DATA_RESULT";
			} else if (category == "工艺") {
				tableName = "CRAFT_DATA_RESULT";
			} else if (category == "质量综合") {
				tableName = "QUALITY_CONTROL_RESULT";
			} else if (category == "过程控制") {
				tableName = "PROCESS_CONTROL_RESULT";
			}
			var entype = Things["Thing.Integration.DataCollectV2"].getEntypeByFileTypeV2({ fileType: type });
			if (entype === '') { continue; }

			var resp = Things["Thing.Integration.DataCollectV2"].postGetProcessListInfoV2({ reqUrl: reqUrl, processCode: processCode, category: category, entype: entype });
			if (resp === '') { continue; }
			var xml = new XML(resp);
			var fileNameCol = Things["Thing.Integration.DataCollectV2"].getFileNameColumnV2({ type: entype });
			var batchIds = Resources["InfoTableFunctions"].CreateInfoTableFromDataShape({ infoTableName: "List", dataShapeName: "StringList" });

			var records = xml.Record;
			for (var i = 0; i < records.length(); i++) {
				var tag = records[i];
				var source_id = tag.Id;
				if (source_id == '') { continue; }
				var productId = tag.ProductId;
				var pdfFileName = "", pdfFilePath = "", pdfFileFormat = "";
				var excelFileName = "", excelFilePath = "", excelFileFormat = "";
				var fileName = "", filePath = "", fileFormat = "";

				if (entype == "CheckCard") {
					var pdfjson = Things["Thing.Integration.DataCollectV2"].downloadFileV2({ url: tag.PDFDownLoadURL });
					if (pdfjson.success) { pdfFileName = pdfjson.fileName; pdfFilePath = pdfjson.filePath; pdfFileFormat = pdfjson.fileFormat || 'pdf'; }
					var exceljson = Things["Thing.Integration.DataCollectV2"].downloadFileV2({ url: tag.ExcelDownLoadURL });
					if (exceljson.success) { excelFileName = exceljson.fileName; excelFilePath = exceljson.filePath; excelFileFormat = exceljson.fileFormat || 'xlsx'; }
				} else if (entype == "Photo") {
					var durl = tag.DownLoadURL;
					if (durl != '') {
						filePath = durl;
						var b = durl.substring(0, durl.indexOf('_'));
						fileFormat = b.substring(b.lastIndexOf('.') + 1);
						var ok = Things["Thing.Integration.DataCollectV2"].photoExtWhitelistV2({ ext: fileFormat });
						if (ok !== 'true') { fileFormat = ""; }
					}
				} else if (entype == "TechCard" || entype == "TechProblem" || entype == "ScrapNotice" || entype == "ProductSubmit" || entype == "UndeliveredProduct" || entype == "UniversalTrackingCard") {
					var json = Things["Thing.Integration.DataCollectV2"].downloadFileV2({ url: tag.DownLoadURL });
					if (json.success) { fileName = json.fileName; filePath = json.filePath; fileFormat = json.fileFormat; }
				}

				source_id = source_id + '_' + tableName;
				var existSql = "select * from XMLDATA_" + entype + " where source_id='" + source_id + "'";
				var rs = Things["Thing.DB.Oracle"].RunQuery({ sql: existSql });
				var resultId = 0, pdf_resultId = 0, excel_resultId = 0;

				if (rs.rows.length == 0) {
					if (entype == "CheckCard") {
						pdf_resultId = Things["Thing.Integration.DataCollectV2"].addOrUpdateResultV2({ tableName: tableName, id: 0, NODECODE: row.NODECODE, NODENAME: row.NODECODE, FILE_NUMBER: "", FILE_NAME: tag[fileNameCol], FILE_TYPE: type, GATHERING_METHOD: "自动采集", SOURCE_SYSTEM: "MES", DELIVERY_STATE: row.DELIVERY_STATE, SECURITY_LEVEL: tag.SecurityLevel, FILEPATH: pdfFilePath, FILENAME: pdfFileName, STATE_CHECK: "未确认", FILE_FORMAT: pdfFileFormat, productId: productId, STATUS: 'new', docInfo: '' });
						resultRowCounter++;
						excel_resultId = Things["Thing.Integration.DataCollectV2"].addOrUpdateResultV2({ tableName: tableName, id: 0, NODECODE: row.NODECODE, NODENAME: row.NODECODE, FILE_NUMBER: "", FILE_NAME: tag[fileNameCol], FILE_TYPE: type, GATHERING_METHOD: "自动采集", SOURCE_SYSTEM: "MES", DELIVERY_STATE: row.DELIVERY_STATE, SECURITY_LEVEL: tag.SecurityLevel, FILEPATH: excelFilePath, FILENAME: excelFileName, STATE_CHECK: "未确认", FILE_FORMAT: excelFileFormat, productId: productId, STATUS: 'new', docInfo: '' });
						resultRowCounter++;
						if (isStructuralAssembly === 'true') {
							if (String(tag[fileNameCol]).indexOf('精度测量') > -1) {
								var result_id = excel_resultId;
								var data_id = source_id;
								var product_id = productId;
								var ref_dpid = row.NODECODE;
								var thisFilePath = fileUploadPath + excelFilePath;
								Things["Thing.Integration.DataCollectV2"].parseStructuralAssemblyFileV2({ ref_dpid: ref_dpid, data_id: data_id, result_id: result_id, product_id: product_id, model: model, thisFilePath: thisFilePath });
							}
						}
					} else if (entype == "MaterialDelivery") {
						var materieResultId = Things["Thing.Integration.DataCollectV2"].getMaterieResultIdV2({ tableName: tableName, nodecode: row.NODECODE, materielCode: tag[fileNameCol] });
						if (materieResultId == -1) {
							resultId = Things["Thing.Integration.DataCollectV2"].addOrUpdateResultV2({ tableName: tableName, id: 0, NODECODE: row.NODECODE, NODENAME: row.NODECODE, FILE_NUMBER: "", FILE_NAME: tag[fileNameCol], FILE_TYPE: type, GATHERING_METHOD: "自动采集", SOURCE_SYSTEM: "MES", DELIVERY_STATE: row.DELIVERY_STATE, SECURITY_LEVEL: tag.SecurityLevel, FILEPATH: filePath, FILENAME: fileName, STATE_CHECK: "未确认", FILE_FORMAT: fileFormat, productId: productId, STATUS: 'new', docInfo: '' });
							resultRowCounter++;
						} else {
							resultId = materieResultId;
						}
					} else {
						resultId = Things["Thing.Integration.DataCollectV2"].addOrUpdateResultV2({ tableName: tableName, id: 0, NODECODE: row.NODECODE, NODENAME: row.NODECODE, FILE_NUMBER: "", FILE_NAME: tag[fileNameCol], FILE_TYPE: type, GATHERING_METHOD: "自动采集", SOURCE_SYSTEM: "MES", DELIVERY_STATE: row.DELIVERY_STATE, SECURITY_LEVEL: tag.SecurityLevel, FILEPATH: filePath, FILENAME: fileName, STATE_CHECK: "未确认", FILE_FORMAT: fileFormat, productId: productId, STATUS: 'new', docInfo: '' });
						resultRowCounter++;
					}
				} else {
					var existRow = rs.rows[0];
					var soId = existRow['SOURCE_ID'];
					if (entype == "CheckCard") {
						pdf_resultId = existRow['PDF_RESULT_ID'];
						Things["Thing.Integration.DataCollectV2"].addOrUpdateResultV2({ tableName: tableName, id: pdf_resultId, NODECODE: row.NODECODE, NODENAME: row.NODECODE, FILE_NUMBER: "", FILE_NAME: tag[fileNameCol], FILE_TYPE: type, GATHERING_METHOD: "自动采集", SOURCE_SYSTEM: "MES", DELIVERY_STATE: row.DELIVERY_STATE, SECURITY_LEVEL: tag.SecurityLevel, FILEPATH: pdfFilePath, FILENAME: pdfFileName, STATE_CHECK: "未确认", FILE_FORMAT: pdfFileFormat || 'pdf', productId: productId, STATUS: 'new', docInfo: '' });
						resultRowCounter++;
						excel_resultId = existRow['EXCEL_RESULT_ID'];
						Things["Thing.Integration.DataCollectV2"].addOrUpdateResultV2({ tableName: tableName, id: excel_resultId, NODECODE: row.NODECODE, NODENAME: row.NODECODE, FILE_NUMBER: "", FILE_NAME: tag[fileNameCol], FILE_TYPE: type, GATHERING_METHOD: "自动采集", SOURCE_SYSTEM: "MES", DELIVERY_STATE: row.DELIVERY_STATE, SECURITY_LEVEL: tag.SecurityLevel, FILEPATH: excelFilePath, FILENAME: excelFileName, STATE_CHECK: "未确认", FILE_FORMAT: excelFileFormat || 'xlsx', productId: productId, STATUS: 'new', docInfo: '' });
						resultRowCounter++;

						if (isStructuralAssembly === 'true') {
							if (String(tag[fileNameCol]).indexOf('精度测量') > -1) {
								var result_id = excel_resultId;
								var data_id = soId;
								var product_id = productId;
								var ref_dpid = row.NODECODE;
								var thisFilePath = fileUploadPath + excelFilePath;
								Things["Thing.DB.Oracle"].RunCommand({ sql: "delete from TJFX_STRUCTURALASSEMBLY where RESULT_ID=" + result_id });
								Things["Thing.Integration.DataCollectV2"].parseStructuralAssemblyFileV2({ ref_dpid: ref_dpid, data_id: data_id, result_id: result_id, product_id: product_id, model: model, thisFilePath: thisFilePath });
							}
						}
					} else {
						resultId = existRow['RESULT_ID'];
						Things["Thing.Integration.DataCollectV2"].addOrUpdateResultV2({ tableName: tableName, id: resultId, NODECODE: row.NODECODE, NODENAME: row.NODECODE, FILE_NUMBER: "", FILE_NAME: tag[fileNameCol], FILE_TYPE: type, GATHERING_METHOD: "自动采集", SOURCE_SYSTEM: "MES", DELIVERY_STATE: row.DELIVERY_STATE, SECURITY_LEVEL: tag.SecurityLevel, FILEPATH: filePath, FILENAME: fileName, STATE_CHECK: "未确认", FILE_FORMAT: fileFormat, productId: productId, STATUS: 'new', docInfo: '' });
						resultRowCounter++;
					}
					Things["Thing.DB.Oracle"].RunCommand({ sql: "delete from XMLDATA_" + entype + " where SOURCE_ID='" + soId + "'" });
				}

				Things["Thing.Integration.DataCollectV2"].insertXmlDataV2({ type: entype, tag: tag, source_id: source_id, resultId: resultId, excelFileName: excelFileName, excelFilePath: excelFilePath, pdfFileName: pdfFileName, pdfFilePath: pdfFilePath, fileName: fileName, filePath: filePath, pdf_resultId: pdf_resultId, excel_resultId: excel_resultId, productId: productId });
				rsLen++;
				var rowIt = Resources["InfoTableFunctions"].CreateInfoTableFromDataShape({ infoTableName: "List", dataShapeName: "StringList" });
				rowIt.AddRow({ name: source_id });
				batchIds.AddRow({ name: source_id });
			}

			// 差异置旧：仅置旧本节点、该表的非本批次
			Things["Thing.Integration.DataCollectV2"].markOldByDifferenceV2({ tableName: tableName, nodecode: row.NODECODE, tableType: entype, batchSourceIds: batchIds });
		}

		var endMs = new Date().getTime();
		var ms = endMs - startMs;
		message = '成功同步' + rsLen + '条业务记录，落表' + resultRowCounter + '条，耗时：' + ms + 'ms';
		result = message;
	}
} catch (e) {
	result = '成功同步' + rsLen + '条后中断：' + e;
}



