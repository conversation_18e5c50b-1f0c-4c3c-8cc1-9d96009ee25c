var funcIdent = "report";
var planTableAlign = "left"; //策划确认表的对齐方式
//初始化layui对象
layui.use(['layer', 'form', 'upload', 'table', 'laydate', 'dropdown'], function () {
	layer = layui.layer;
	upload = layui.upload;
	table = layui.table;
	form = layui.form;
	laydate = layui.laydate;
	dropdown = layui.dropdown;
	device = layui.device();
});

$(function () {
	loadTree();

	$("#root_layout").layout("panel", "center").panel({
		onResize: function (width, height) {
			//2是边框宽度
			var gridWidth = width - 2;
			var gridId = "";
			if (tabId == 'quality_tab') {
				gridId = "qualitySummaryTable";
			} else if (tabId == 'quality_photo_tab') {
				gridId = "photoSummaryTable";
			}
			if (gridId != "") {
				try {
					$("#" + gridId).datagrid('resize', {
						width: gridWidth
					});
					changeWidth(gridId);
				} catch (e) {

				}
			}
		}
	});
	//绑定事件
	$('#tabs').tabs({
		onSelect: function (title, index) {
			var tab = $('#tabs').tabs('getTab', index);
			tabId = tab.panel('options').tableId;
			var selNode = getSelTreeNode();
			if (selNode.TYPE == 'report' || selNode.TYPE.indexOf('table') > -1) {

			} else {
				loadData(selNode, tabId);
			}
		}
	});
})
var tabId = "quality_tab";
var THING = 'Thing.Fn.QualityReport';
var qualityPhoto = void 0;
var contextEle; //签名td元素
//控制tab页签是否可用
var controlTab = function (treeNode) {
	if (treeNode.TYPE == 'report' || treeNode.TYPE.indexOf('table') > -1) {
		$('#tabs').tabs('enableTab', '确认表格');
		$('#tabs').tabs('disableTab', '质量影像记录');
		$('#tabs').tabs('disableTab', '策划质量数据');
		$('#tabs').tabs('select', '确认表格');
	} else {
		$('#tabs').tabs('disableTab', '确认表格');
		$('#tabs').tabs('enableTab', '质量影像记录');
		$('#tabs').tabs('enableTab', '策划质量数据');
		if (tabId == 'quality_report_tab') {
			$('#tabs').tabs('select', '策划质量数据');
		}
	}
}
var nodeClick = function (treeNode, tabId) {
	controlTab(treeNode);
	loadData(treeNode, tabId);
}

var loadData = function (treeNode, tabId) {
	if (treeNode.TYPE == 'report' || treeNode.TYPE.indexOf('table') > -1) {
		reloadTable(treeNode);
	} else {
		if (tabId == 'quality_tab') {
			new ProcessQuality();
		} else if (tabId == 'quality_photo_tab') {
			loadQualityPhoto(true);
		}
	}
}

var loadQualityPhoto = function (isInit) {

	if (qualityPhoto == undefined) {
		qualityPhoto = new QualityPhoto();
	}
	if (isInit) {
		qualityPhoto.init();
	}
}

function exportData(treeNode, dlwIsAll) {
	var loading;
	var url = fileHandlerUrl + "/table/second/export";
	$.fileDownload(url, {
		httpMethod: 'POST',
		data: {
			"processTreeId": treeNode.TREE_ID,
			"tableId": treeNode.DATA_TYPE,
			"dlwIsAll": dlwIsAll,
			"query": JSON.stringify({
				"queryUser": sessionStorage.getItem('username')
			})
		},
		prepareCallback: function (url) {
			loading = layer.msg("正在导出...", {
				icon: 16,
				shade: 0.3,
				time: 0
			});
		},
		abortCallback: function (url) {
			layer.close(loading);
			layer.msg("导出异常！！");
		},
		successCallback: function (url) {
			layer.close(loading);
		},
		failCallback: function (html, url) {
			layer.close(loading);
			layer.msg("导出失败！！");
		}
	});
}

//保存配置信息
function saveConfig(treeNode) {


	$('#dlw-export-all').unbind("click").bind('click', function () {
		exportData(treeNode, 1);
	});

	//汇总表 导出excel
	$('#exportExcel').unbind("click").bind('click', function () {
		exportData(treeNode, 2);
	});

	$("#viewHtml").unbind("click").bind('click', function () {
		var treeId = treeNode.ID;
		var url = fileHandlerUrl + "/api/ext/tableData/" + treeId;
		//ajax
		$.ajax({
			url: url,
			type: 'GET',
			dataType: 'json',
			success: function (res) {
				if (res.success) {
					var sessionKey = 'quality_report_' + new Date().getTime() + '_' + Math.random().toString(36).substr(2);
					sessionStorage.setItem(sessionKey, res.data);
					var url = "viewTable/viewTable.html";
					window.open(url + '?sessionKey=' + sessionKey);
				} else {
					layer.alert(res.msg, {
						icon: 2
					});
				}
			}
		});
	});


	$('#saveConfig').unbind("click").bind('click', function () {
		//获取数据来源
		var dataSource = $('#dataSource').combobox('getValue');
		if (dataSource == '') {
			layer.alert("请选择数据来源！");
			return false;
		}

		//获取自动填充类型
		var dataType = $('#dataType').combobox('getValue');
		//获取报告类型
		var reportType = $('#reportType').combobox('getValue');

		//自动关联
		if (dataSource == 'auto') {
			if (dataType == '') {
				layer.alert("请选择自动填充类型！");
				return false;
			}
			if (reportType == '') {
				layer.alert("请选择报告类型！");
				return false;
			}
		}
		var cb_success = function (res) {
			if (res.success) {
				layer.msg(res.msg);
				//更新树节点的数据
				treeNode.DATA_SOURCE = dataSource;
				treeNode.DATA_TYPE = dataType;
				treeNode.REPORT_TYPE = reportType;
				ztreeObj.updateNode(treeNode);
				reloadTable(treeNode);
			} else {
				layer.alert(res.msg);
			}
		};

		var cb_error = function (err) {
			layer.alert("请求失败！");
		};

		twxAjax(THING, "SaveConfig", {
			id: treeNode.ID,
			dataSource: dataSource,
			dataType: dataType,
			reportType: reportType
		}, true, cb_success, cb_error);
	});
}

//加载数据配置下拉框
function loadCombobox(treeNode) {
	$("#dataConfig").show();
	var dataSource = treeNode.DATA_SOURCE || '';
	var dataType = treeNode.DATA_TYPE || '';
	var reportType = treeNode.REPORT_TYPE || '';

	$('#dataSource').combobox({
		valueField: 'value',
		textField: 'text',
		panelHeight: 'auto',
		data: [{
			value: 'manual',
			text: '手动填写'
		}, {
			value: 'auto',
			text: '自动填充'
		}, {
			value: 'list',
			text: '文件清单'
		}, {
			value: 'test',
			text: '试验管控系统'
		}],
		onSelect: function (record) {
			if (record.value == 'auto' || record.value == 'test') {
				$(".auto").show();

				var cb_success = function (res) {
					if (res.success) {
						//加载数据类型下拉框
						$('#dataType').combobox({
							valueField: 'ID',
							textField: 'TREE_NAME',
							panelHeight: 400,
							data: res.data
						});
						var isIn = false;
						for (var i = 0; i < res.data.length; i++) {
							if (dataType == res.data[i].ID) {
								isIn = true;
								break;
							}
						}
						if (isIn) {
							$('#dataType').combobox('select', dataType);
						}

						if (record.value == 'auto') {
							$(".reportType").show();
							//加载报告类型下拉框
							$('#reportType').combobox({
								valueField: 'value',
								textField: 'text',
								panelHeight: 'auto',
								data: [{
									value: 'confirm',
									text: '策划确认表'
								}, {
									value: 'summary',
									text: '策划汇总表'
								}, {
									value: 'actual',
									text: '实际汇总表'
								}, {
									value: 'photo',
									text: '影像记录表'
								}]
							});
							$('#reportType').combobox('select', reportType);
						} else {
							$(".reportType").hide();
						}

					} else {
						layer.alert(res.msg);
					}
				};
				var cb_error = function (xhr) {
					layer.alert('请求失败!', {
						icon: 2
					});
				};
				twxAjax(THING, "QueryDataType", {
					treeId: treeNode.TREE_ID,
					dataSource: record.value
				}, true, cb_success, cb_error);
			} else {
				$(".auto").hide();
			}
		}
	});

	$('#dataSource').combobox('select', dataSource);
}

//加载左侧的表格
function reloadTable(treeNode, scrollObj) {
	$("#exportExcel").hide();
	$("#dlw-export-all").hide();
	var type = treeNode.TYPE;
	if (type.indexOf('table') > -1 || type == 'report') {
		var loadIndex = layer.load();
		saveConfig(treeNode);
		loadCombobox(treeNode);
		if (treeNode.DATA_SOURCE == 'auto') {
			$("#msg").hide();
			$("#tbr").hide();
			$("#table").empty();
			$("#table").show();
			$("#table").css({
				"height": "auto",
				"padding": "0px 10px",
				"overflow": "auto"
			});
			$("#table-security1").show();
			$("#table-security2").hide();
			$(".table-security").text(HotUtil.getSecurityName(treeNode.SECURITY));
			var tableConfigId = treeNode.DATA_TYPE;
			var reportType = treeNode.REPORT_TYPE;
			var treeId = treeNode.TREE_ID;

			twxAjax(THING, "QueryAutoTable", {
				id: treeNode.ID,
				username: sessionStorage.getItem('username')
			}, true, function (res) {
				if (res.success) {
					if (reportType == "confirm") {
						loadAutoConfirmTable(res);
					} else if (reportType == "summary" || reportType == "actual") {
						if (reportType == "actual") {
							$("#exportExcel").show();
							if ($('#dataType').combobox("getText") == '电缆网元器件汇总表') {
								$("#dlw-export-all").show();
							} else {
								$("#dlw-export-all").hide();
							}

						}
						loadAutoSummaryTable(res, tableConfigId);
					} else if (reportType == "photo") {
						loadAutoPhotoTable(res, tableConfigId, treeId);
					}
				} else {
					$("#msg").text(res.msg).show();
				}
				layer.close(loadIndex);
			}, function (err) {
				layer.close(loadIndex);
				layer.alert('请求失败');
			});
		} else {
			$("#msg").hide();
			$("#tbr").show();
			$("#table").show();
			$("#table").css("padding", "0px 10px").css("height", 'auto');
			$("#table-security2").show();
			$("#table-security1").hide();
			initTbr(treeNode);
			var service = "QueryNodeById";
			if (treeNode.DATA_SOURCE == 'list') {
				service = "QueryFileList";
			} else if (treeNode.DATA_SOURCE == 'test') {
				service = "QueryTestTable";
			}
			twxAjax(THING, service, {
				id: treeNode.ID,
			}, true, function (res) {
				var otherHeight = treeNode.TABLE_STATUS == 'sign' ? 100 : 140;
				HotUtil.loadHtmlTable(res, treeNode, $("#quality_report_tab #table"), otherHeight, funcIdent,
					scrollObj);
				layer.close(loadIndex);
			},
				function (err) {
					layer.close(loadIndex);
					layer.alert('请求失败');
				});
		}
	} else {
		$("#msg").text("请选择表节点！").show();
		$("#tbr").hide();
		$("#table").hide();
	}
}

//加载树结构
function loadTree() {
	var cb_success = function (res) {
		if (res.success) {
			var datas = res.data;
			if (datas.length > 0) {
				datas = dealAitDataIcons(datas);
				datas = dealAitDataNodeName(datas);
				treeSetting.callback.onClick = function (event, treeId, treeNode) {
					nodeClick(treeNode, tabId);
				};
				treeSetting.callback.onExpand = function (event, treeId, treeNode) {
					loadTreeMenu();
				};
				ztreeObj = $.fn.zTree.init($("#dpTree"), treeSetting, datas);
				var nodes = ztreeObj.getNodes();
				for (var i = 0; i < nodes.length; i++) { //设置节点展开ss
					ztreeObj.expandNode(nodes[i], true, false, true);
				}
				loadTreeMenu();
				loadData(getSelTreeNode(), tabId);
				locateNode();
			}
		} else {
			layer.alert(res.msg);
		}

	};
	//使用ajax进行异步加载Tree
	twxAjax('Thing.Fn.ProcessTree', 'QueryTreeRoot', {
		username: sessionStorage.getItem('username')
	}, true, cb_success);
}

//加载树节点右键菜单
function loadTreeMenu() {
	$("#dpTree a").each(function (i, n) {
		var node = ztreeObj.getNodeByTId($(n).parent().attr("id"));
		menu = getNodeMenu(node);
		if (menu.length != 0) {
			$(n).contextMenu({
				width: 140,
				menu: menu,
				target: function (ele) {
					var node = ztreeObj.getNodeByTId($(ele).parent().attr("id"));
					ztreeObj.selectNode(node, false, true);
				}
			});
		}
	});
}

/**
 * 操作完节点之后重新加载节点
 * @param {*} refrushId 需要刷新的树节点
 * @param {*} selId 需要选中的树节点
 */
function reloadTree(refrushId, selId) {
	if (selId) {

	} else {
		selId = refrushId;
	}
	var refrushTreeNode = ztreeObj.getNodeByParam("ID", refrushId, null);
	if (!refrushTreeNode.ISPARENT) {
		refrushTreeNode.ISPARENT = true;
		ztreeObj.updateNode(refrushTreeNode);
	}
	ztreeObj.reAsyncChildNodes(refrushTreeNode, 'refresh', false, function () {
		if (refrushTreeNode.children.length == 0) {
			refrushTreeNode.ISPARENT = false;
			ztreeObj.updateNode(refrushTreeNode);
			ztreeObj.expandNode(refrushTreeNode, true, false, true);
		}
		var newSelNode = ztreeObj.getNodeByParam("ID", selId, null);
		ztreeObj.selectNode(newSelNode, false, true);
		loadTreeMenu();
		reloadTable(newSelNode);
	});
}
