/**
 * @definition    getMaterieResultIdV2
 * @description   物料发料记录对应结果ID（去重用） wanghq
 * @implementation    {Script}
 *
 * @param    {STRING}    tableName
 * @param    {NUMBER}    nodecode
 * @param    {STRING}    materielCode
 *
 * @returns    {NUMBER}
 */
var sql = "select id from " + tableName + " where NODECODE='" + nodecode + "' and FILE_TYPE='领料记录' and GATHERING_METHOD='自动采集' and FILE_NAME='" + materielCode + "'";
var rs = Things['Thing.DB.Oracle'].RunQuery({ sql: sql });
if (rs.rows.length > 0) {
	result = rs.rows[0].ID;
} else {
	result = -1;
}


