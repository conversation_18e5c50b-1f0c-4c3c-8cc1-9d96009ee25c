/**
 * @definition    SyncMESModelProgress
 * @description   同步MES型号进度 wanghq 2025年4月17日16:22:26
 * @implementation    {Script}
 *
 *
 * @returns    {JSON}
 */
var res = {};
try {
    //获取项目清单接口路径
    var reqUrl = Things["Thing.Fn.SystemDic"].getKeyByNames({
        name: 'MES项目清单接口路径',
        pname: '系统配置'
    });

    var content =
        '<?xml version="1.0" encoding="utf-8"?>\
			    <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">\
			      <soap:Body>\
			        <RetrieveDailyTaskByWPId xmlns="http://tempuri.org/">\
			        </RetrieveDailyTaskByWPId>\
			      </soap:Body>\
			    </soap:Envelope>';

    //PostXML所需参数
    var params = {
        headers: {
            "Content-Type": "text/xml; charset=utf-8"
        },
        url: reqUrl,
        timeout: 6000000,
        content: content
    };
    var resultXml = Resources["ContentLoaderFunctions"].PostXML(params);
    //解析返回的xml
    var contentXml = resultXml.*:: Body.*:: RetrieveDailyTaskByWPIdResponse;

    resultXml = String(contentXml.*:: RetrieveDailyTaskByWPIdResult);
    resultXml = resultXml.substring(resultXml.indexOf("<Response"), resultXml.indexOf("</Response>") + 11);
    //结果集的xml
    var xml = new XML(resultXml);

    // 统计计数器
    var insertCount = 0;
    var skipCount = 0;
    var errorCount = 0;

    // 第一步：汇总所有model_code
    var modelCodeMap = {};
    for each(var tag in xml.Item) {
        try {
            var code = String(tag.Code);
            if (code && code.trim() !== '') {
                modelCodeMap[code] = true;
            }
        } catch (itemError) {
            logger.warn("汇总model_code时发生错误，错误信息: " + itemError);
        }
    }

    // 获取所有唯一的model_code
    var modelCodes = [];
    for (var code in modelCodeMap) {
        modelCodes.push(code);
    }

    // 第二步：批量删除这些model_code对应的所有记录
    if (modelCodes.length > 0) {
        var deleteSql = "DELETE FROM MES_MODEL_PROGRESS WHERE MODEL_CODE IN ('" + modelCodes.join("','") + "')";
        try {
            Things["Thing.DB.Oracle"].RunCommand({
                sql: deleteSql
            });
            logger.info("批量删除完成，共删除 " + modelCodes.length + " 个型号的数据");
        } catch (deleteError) {
            logger.warn("批量删除时发生错误: " + deleteError);
        }
    }

    // 第三步：处理结果集 - 依次插入item的数据
    for each(var tag in xml.Item) {
        try {
            var code = String(tag.Code);
            var stage = String(tag.Stage);
            var developState = String(tag.DevelopState);

            // 检查必要字段是否为空，如果为空则跳过
            if (!developState || developState.trim() === '' ||
                !code || code.trim() === '' ||
                !stage || stage.trim() === '') {
                logger.info("跳过空字段的记录，MODEL_CODE: " + code + ", STAGE: " + stage + ", DEVELOP_STATE: " + developState);
                continue;
            }

            // 检查是否已存在相同的记录（code + stage + developState）
            var checkSql = "SELECT COUNT(*) as CNT FROM MES_MODEL_PROGRESS WHERE MODEL_CODE = '" + code + "' AND CODE = '" + stage + "' AND DEVELOP_STAGE = '" + developState + "'";
            var checkResult = Things["Thing.DB.Oracle"].RunQuery({
                sql: checkSql
            });

            if (checkResult && checkResult.rows && checkResult.rows.length > 0 && checkResult.rows[0].CNT > 0) {
                skipCount++;
                logger.info("跳过已存在的记录，MODEL_CODE: " + code + ", STAGE: " + stage + ", DEVELOP_STATE: " + developState);
                continue;
            }

            // 执行插入操作
            Things["Thing.DB.Oracle"].RunCommand({
                sql: "INSERT INTO MES_MODEL_PROGRESS(MODEL_CODE, CODE, DEVELOP_STAGE, SYNC_TIME) VALUES('" + code + "', '" + stage + "', '" + developState + "', SYSDATE)"
            });

            // 统计插入数量
            insertCount++;

        } catch (itemError) {
            errorCount++;
            logger.warn("处理单条数据时发生错误，MODEL_CODE: " + code + "，错误信息: " + itemError);
        }
    }

    res.success = true;
    res.data = {
        insertCount: insertCount,
        skipCount: skipCount,
        errorCount: errorCount,
        totalCount: insertCount + skipCount + errorCount
    };
    res.msg = "SyncMESModelProgress同步MES型号进度成功，共处理" + (insertCount + skipCount + errorCount) +
        "条数据（插入:" + insertCount + "条" + (skipCount > 0 ? "，跳过:" + skipCount + "条" : "") + (errorCount > 0 ? "，失败:" + errorCount + "条" : "") + "）";

    // 记录详细的同步结果
    logger.info(res.msg);

} catch (error) {
    res.success = false;
    res.msg = "SyncMESModelProgress同步MES型号进度失败，原因：" + error;
    logger.error(res.msg);
}
result = res;