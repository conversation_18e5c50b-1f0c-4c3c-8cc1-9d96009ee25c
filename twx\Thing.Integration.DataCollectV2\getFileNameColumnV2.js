/**
 * @definition    getFileNameColumnV2
 * @description   根据类型获取文件名称列（V2）  wanghq
 * @implementation    {Script}
 *
 * @param    {STRING}    type
 *
 * @returns    {STRING}
 */
if (type == "TechCard") {
	result = 'AsmTaskName';
} else if (type == "CheckCard") {
	result = 'CheckCardName';
} else if (type == "TechProblem") {
	result = 'BillCode';
} else if (type == "Photo") {
	result = 'PhotoName';
} else if (type == "ScrapNotice") {
	result = 'BillCode';
} else if (type == "MaterialDelivery") {
	result = 'BillCode';
} else if (type == "ProductSubmit") {
	result = 'BillCode';
} else if (type == "UndeliveredProduct") {
	result = 'BillCode';
} else if (type == "ProductOutIn") {
	result = 'BillCode';
} else if (type == "UniversalTrackingCard") {
	result = 'TaskName';
} else {
	result = '';
}


