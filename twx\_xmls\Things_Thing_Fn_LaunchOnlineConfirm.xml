<?xml version="1.0" encoding="UTF-8" standalone="yes"?><Entities build="b68" majorVersion="8" minorVersion="3" modelPersistenceProviderPackage="PostgresPersistenceProviderPackage" revision="9" schemaVersion="1032" universal=""><Things><Thing description="" documentationContent="" effectiveThingPackage="ConfiguredThing" enabled="true" homeMashup="" identifier="" lastModifiedDate="2025-08-21T15:07:27.407+08:00" name="Thing.Fn.LaunchOnlineConfirm" projectName="" published="false" tags="" thingTemplate="Thing.Tpl.OnlineConfirm" valueStream=""><avatar/><DesignTimePermissions><Create/><Read/><Update/><Delete/><Metadata/></DesignTimePermissions><RunTimePermissions/><VisibilityPermissions><Visibility/></VisibilityPermissions><ConfigurationTableDefinitions/><ConfigurationTables/><ThingShape><PropertyDefinitions/><ServiceDefinitions><ServiceDefinition aspect.isAsync="false" category="" description="datetime 2025年5月14日23:03:02" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="CheckDirectoryUpdateStatus"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="STRING" name="taskId" description="" ordinal="0"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="创建或更新同步状态表 wanghq 2024年11月29日09:44:28" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="CreateOrUpdateSyncStatus"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="STRING" name="nodeId" description="" ordinal="0"/><FieldDefinition baseType="STRING" name="username" description="" ordinal="1"/><FieldDefinition baseType="STRING" name="syncStatus" description="" ordinal="2" aspect.defaultValue="ACTIVE"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="创建同步状态表 wanghq 2024年11月29日09:44:28" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="CreateSyncStatus"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="STRING" name="nodeId" description="" ordinal="0"/><FieldDefinition baseType="STRING" name="username" description="" ordinal="1"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="datetime 2025年5月14日23:06:07" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="ExportMorePdf"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="postId" description="" ordinal="0"/><FieldDefinition baseType="NUMBER" name="postPId" description="" ordinal="1"/><FieldDefinition baseType="STRING" name="pageSize" description="" ordinal="2"/><FieldDefinition baseType="STRING" name="pageOrientation" description="" ordinal="3"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="从PDF中提取页码信息 datetime 2025年5月14日23:10:33" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="ExtractPdfPageNumbers"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="STRING" name="pdfPath" description="" ordinal="0"/><FieldDefinition baseType="STRING" name="bTables" description="" ordinal="1"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2024年11月29日10:50:03" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="GetAllCommitmentNodeSql"><ResultType baseType="STRING" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="nodeId" description="" ordinal="0"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="加载AIT同步结构树 wanghq 2025年8月21日10:47:30" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="LoadAitSyncTree"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="STRING" name="modelName" description="型号名称" ordinal="0"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="datetime 2025年5月14日23:06:07" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="ProcessDirectoryUpdateTask"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="STRING" name="taskId" description="" ordinal="0"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="查询分级承诺节的所属型号 wanghq 2024年11月29日11:03:00" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryCommitmentModel"><ResultType baseType="STRING" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="nodeId" description="" ordinal="0"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="查询映射关系表中的AIT表ID wanghq 2024年11月29日10:55:03" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryMappingAitTableId"><ResultType baseType="NUMBER" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="STRING" name="tableName" description="" ordinal="0"/><FieldDefinition baseType="STRING" name="modelName" description="" ordinal="1"/><FieldDefinition baseType="INFOTABLE" name="mappingData" description="" ordinal="2"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="恢复同步 wanghq 2024年11月29日11:16:49" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="RecoverSync"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="nodeId" description="" ordinal="0"/><FieldDefinition baseType="STRING" name="username" description="" ordinal="1"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="终止同步 wanghq 2024年11月29日09:39:45" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="StopSync"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="nodeId" description="" ordinal="0"/><FieldDefinition baseType="STRING" name="username" description="" ordinal="1"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2024年11月28日10:49:23" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="SyncCommitmentData"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="STRING" name="nodeId" description="" ordinal="0"/><FieldDefinition baseType="STRING" name="username" description="" ordinal="1" aspect.defaultValue="adm"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="单个表同步的核心逻辑 wanghq 2025年1月15日" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="SyncSingleTable"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="STRING" name="tableId" description="B表ID" ordinal="0"/><FieldDefinition baseType="STRING" name="aitTableId" description="AIT表ID" ordinal="1"/><FieldDefinition baseType="STRING" name="username" description="用户名" ordinal="2" aspect.defaultValue="adm"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="UpdateDirectory datetime 2025年5月14日17:24:35" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="UpdateDirectory"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="nodeId" description="" ordinal="0"/><FieldDefinition baseType="STRING" name="username" description="" ordinal="1"/><FieldDefinition baseType="STRING" name="pageSize" description="" ordinal="2"/><FieldDefinition baseType="STRING" name="pageOrientation" description="" ordinal="3"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="更新同步状态表 wanghq 2024年11月29日11:13:22" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="UpdateSyncStatus"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="STRING" name="nodeId" description="" ordinal="0"/><FieldDefinition baseType="STRING" name="username" description="" ordinal="1"/><FieldDefinition baseType="STRING" name="syncStatus" description="" ordinal="2" aspect.defaultValue="ACTIVE"/></ParameterDefinitions></ServiceDefinition></ServiceDefinitions><EventDefinitions/><ServiceMappings/><ServiceImplementations><ServiceImplementation description="" handlerName="Script" name="CheckDirectoryUpdateStatus"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    CheckDirectoryUpdateStatus
 * @description   datetime 2025年5月14日23:03:02
 * @implementation    {Script}
 *
 * @param    {STRING}    taskId    
 *
 * @returns    {JSON}
 */
var res = {};
try {
    // 查询任务状态
    var taskSql = "SELECT TASK_ID, STATUS, MESSAGE, CREATED_TIME, UPDATED_TIME FROM DIRECTORY_UPDATE_TASKS WHERE TASK_ID = '" + taskId + "'";
    var taskRows = Things['Thing.DB.Oracle'].RunQuery({sql: taskSql}).rows;
    
    if (taskRows.length === 0) {
        throw "任务不存在";
    }
    
    var task = taskRows[0];
    
    // 返回任务状态
    res.success = true;
    res.status = task.STATUS;
    res.msg = task.MESSAGE;
    res.createdTime = task.CREATED_TIME;
    res.updatedTime = task.UPDATED_TIME;
    
} catch (error) {
    res.success = false;
    var msg = "[CheckDirectoryUpdateStatus]-操作失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="CreateOrUpdateSyncStatus"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    CreateOrUpdateSyncStatus
 * @description   创建或更新同步状态表 wanghq 2024年11月29日09:44:28
 * @implementation    {Script}
 *
 * @param    {STRING}    nodeId    
 * @param    {STRING}    username    
 * @param    {STRING}    syncStatus        {"aspect.defaultValue":"ACTIVE"}
 *
 * @returns    {JSON}
 */
var res = {};
try {
    //查询分级承诺节点以及分级承诺下的所有B表
    var sql = "select a.ID, " +
        "       a.PID, " +
        "       a.NAME, " +
        "       a.TYPE, " +
        "       b.COMMITMENT_TABLE_ID, " +
        "       b.SYNC_STATUS " +
        "from (select ID, PID, NAME, TYPE " +
        "      from LAUNCH_CONFIRM " +
        "      start with ID = " + nodeId + " " +
        "      connect by prior ID = PID " +
        "      order by LEVEL_NUM, SORT) a " +
        "         left join " +
        "     COMMITMENT_SYNC_RELATION b on a.ID = b.COMMITMENT_TABLE_ID ";

    var allNode = Things['Thing.DB.Oracle'].RunQuery({ sql: sql }).rows;

    var modelName = '';
    if (allNode.length &gt; 0) {
        var aNode = allNode[0];
        //查询父节点获取型号名称
        var sql = 'select NAME from LAUNCH_CONFIRM where ID=' + aNode.PID;
        var pNodeRows = Things['Thing.DB.Oracle'].RunQuery({ sql: sql }).rows;
        if (pNodeRows.length &gt; 0) {
            var pNode = pNodeRows[0];
            modelName = pNode['NAME'];
        }
    }

    //查询映射关系表 将B表与Ait的表建立关系
    var mappingSql = 'select * from LAUNCH_CONFIRM_MAPPING';
    var mappingData = Things['Thing.DB.Oracle'].RunQuery({ sql: mappingSql }).rows;

    function queryAitTableId(tableName) {
        var aitTableId = -1;
        var professionalName = '';
        var processName = '';
        var aitTableName = '';
        for (var j = 0; j &lt; mappingData.length; j++) {
            var mapping = mappingData[j];
            if (mapping['COMMITMENT_TABLE'] == tableName) {
                professionalName = mapping['PROFESSIONAL_NODE'];
                processName = mapping['PROCESS_NODE'];
                aitTableName = mapping['CONFIRM_TABLE_NAME'];
                break;
            }
        }
        if (professionalName !== '' &amp;&amp; aitTableName !== '' &amp;&amp; modelName !== '') {
            var queryTreeIdSql = "select TREEID from TREE_ALL_VIEW where MODEL_NAME = '" + modelName + "' and PHASE_NAME = '正样阶段' and DIR_NAME = '" + professionalName + "'";
            if (processName) {
                queryTreeIdSql += " and LEAF_NAME = '" + processName + "' and NODETYPE='leaf'";
            } else {
                queryTreeIdSql += " and NODETYPE='dir'";
            }
            var queryTreeIdRows = Things['Thing.DB.Oracle'].RunQuery({ sql: queryTreeIdSql }).rows;
            if (queryTreeIdRows.length &gt; 0) {
                var treeId = queryTreeIdRows[0]['TREEID'];
                var queryAitTableIdSql = "select ID from QUALITY_REPORT where TREE_ID=" + treeId + " and NAME='" + aitTableName + "'";
                var queryAitTableIdRows = Things['Thing.DB.Oracle'].RunQuery({ sql: queryAitTableIdSql }).rows;
                if (queryAitTableIdRows.length &gt; 0) {
                    aitTableId = queryAitTableIdRows[0]['ID'];
                }
            }
        }
        return aitTableId;
    }

    //遍历allNode下的所有B表 根据映射关系表建立关系
    for (var i = 0; i &lt; allNode.length; i++) {
        var node = allNode[i];
        var tableId = node['ID'];
        var tableName = node['NAME'];
        var nodeSyncStatus = node['SYNC_STATUS'];
        var commandSql = '';
        if (nodeSyncStatus) {
            if (syncStatus == 'ACTIVE' &amp;&amp; nodeSyncStatus == 'STOPPED') {
                //恢复同步操作： 更新恢复同步时间和恢复同步人
                commandSql = "update COMMITMENT_SYNC_RELATION set SYNC_STATUS='" + syncStatus
                    + "', RECOVER_TIME=SYSDATE,RECOVER_USER='" + username + "' where COMMITMENT_TABLE_ID=" + tableId;
            } else if (syncStatus == 'STOPPED' &amp;&amp; nodeSyncStatus == 'ACTIVE') {
                //终止同步操作： 更新终止同步时间和终止同步人
                commandSql = "update COMMITMENT_SYNC_RELATION set SYNC_STATUS='" + syncStatus
                    + "', STOP_TIME=SYSDATE,STOP_USER='" + username + "' where COMMITMENT_TABLE_ID=" + tableId;
            }
        } else {
            var aitTableId = queryAitTableId(tableName);
            if (syncStatus == 'STOPPED') {
                //终止同步操作： 插入终止同步时间和终止同步人
                commandSql = "insert into COMMITMENT_SYNC_RELATION (ID, COMMITMENT_TABLE_ID, QUALITY_REPORT_ID,SYNC_STATUS,STOP_TIME,STOP_USER,CREATE_TIME,CREATE_USER) " +
                    "values (SEQ_COMMITMENT_SYNC_RELATION.NEXTVAL, " + tableId + ", " + aitTableId + ", '" + syncStatus + "', SYSDATE, '" + username + "',SYSDATE, '" + username + "')";
            } else {
                commandSql = "insert into COMMITMENT_SYNC_RELATION (ID, COMMITMENT_TABLE_ID, QUALITY_REPORT_ID,SYNC_STATUS,CREATE_TIME,CREATE_USER) " +
                    "values (SEQ_COMMITMENT_SYNC_RELATION.NEXTVAL, " + tableId + ", " + aitTableId + ", '" + syncStatus + "', SYSDATE, '" + username + "')";
            }
        }
        Things['Thing.DB.Oracle'].RunCommand({ sql: commandSql });
    }
    res.success = true;
} catch (error) {
    res.success = false;
    res.msg = "CreateOrUpdateSyncStatus失败，原因：" + error;
    logger.error(res.msg);
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="CreateSyncStatus"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    CreateSyncStatus
 * @description   创建同步状态表 wanghq 2024年11月29日09:44:28
 * @implementation    {Script}
 *
 * @param    {STRING}    nodeId    
 * @param    {STRING}    username    
 *
 * @returns    {JSON}
 */
var res = {};
try {

    //查询分级承诺节点以及分级承诺下的所有B表
    var allNode = Things['Thing.DB.Oracle'].RunQuery({ sql: me.GetAllCommitmentNodeSql({ nodeId: nodeId }) }).rows;

    var modelName = me.QueryCommitmentModel({ nodeId: nodeId });

    //查询映射关系表 将B表与Ait的表建立关系
    var mappingData = Things['Thing.DB.Oracle'].RunQuery({ sql: 'select * from LAUNCH_CONFIRM_MAPPING' });


    //遍历allNode下的所有B表 根据映射关系表建立关系
    for (var i = 0; i &lt; allNode.length; i++) {
        var node = allNode[i];
        var tableId = node['ID'];
        var tableName = node['NAME'];
        var nodeSyncStatus = node['SYNC_STATUS'];
        var aitTableId = me.QueryMappingAitTableId({ tableName: tableName, modelName: modelName, mappingData: mappingData });
        var commandSql = '';
        if (nodeSyncStatus) {
            commandSql = "update COMMITMENT_SYNC_RELATION set QUALITY_REPORT_ID=" + aitTableId + " where COMMITMENT_TABLE_ID=" + tableId;
        } else {
            commandSql = "insert into COMMITMENT_SYNC_RELATION (ID, COMMITMENT_TABLE_ID, QUALITY_REPORT_ID,CREATE_TIME,CREATE_USER) " +
                "values (SEQ_COMMITMENT_SYNC_RELATION.NEXTVAL, " + tableId + ", " + aitTableId + ",  SYSDATE, '" + username + "')";
        }
        Things['Thing.DB.Oracle'].RunCommand({ sql: commandSql });
    }
    res.success = true;
} catch (error) {
    res.success = false;
    res.msg = "CreateSyncStatus失败，原因：" + error;
    logger.error(res.msg);
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="ExportMorePdf"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    ExportMorePdf
 * @description   datetime 2025年5月14日23:06:07
 * @implementation    {Script}
 *
 * @param    {NUMBER}    postId    
 * @param    {NUMBER}    postPId    
 * @param    {STRING}    pageSize    
 * @param    {STRING}    pageOrientation    
 *
 * @returns    {JSON}
 */
var res = {};
try {
    // 设置默认的PDF选项
    if (!pageSize) pageSize = "A4";
    if (!pageOrientation) pageOrientation = "landscape";
    var postPPId = Things['Thing.DB.Oracle'].RunQuery({sql: "SELECT PID FROM LAUNCH_CONFIRM WHERE ID = " + postPId}).rows[0]['PID'];
    // 使用新的接口，该接口返回文件路径而不是二进制数据
    var baseUrl = Things['Thing.System'].GetFileHandleUrl() + "/online/export/more/pdf/path";
    var url = baseUrl + "?id=" + postPId + "&amp;pid=" + postPPId + "&amp;thing=Thing.Fn.LaunchOnlineConfirm";

    // 添加PDF选项参数
    url += "&amp;pageSize=" + pageSize + "&amp;pageOrientation=" + pageOrientation;

    // 构建请求配置
    var requestParams = {
        url: url,
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            "Accept": "application/json"
        },
        usePostMethod: true
    };

    var response = Resources["ContentLoaderFunctions"].PostJSON(requestParams);

    // 解析响应结果
    // 直接使用response对象，它已经是JSON格式
    if (response.success) {
        res.success = true;
        res.data = response.data; // 文件路径
        res.msg = response.msg || "PDF导出成功";
    } else {
        throw response.msg || "服务器返回失败状态";
    }
} catch (error) {
    res.success = false;
    var msg = "[ExportMorePdf]-操作失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="ExtractPdfPageNumbers"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    ExtractPdfPageNumbers
 * @description   从PDF中提取页码信息 datetime 2025年5月14日23:10:33
 * @implementation    {Script}
 *
 * @param    {STRING}    pdfPath    
 * @param    {STRING}    bTables    
 *
 * @returns    {JSON}
 */
var res = {};
try {
    // 解析B表信息
    var bTablesArray = JSON.parse(bTables);

    // 构建带参数的请求URL
    var baseUrl = Things['Thing.System'].GetFileHandleUrl() + "/online/extract/pdf/pages";
    var url = baseUrl + "?pdfPath=" + encodeURIComponent(pdfPath) + "&amp;bTables=" + encodeURIComponent(bTables);

    // 构建请求配置
    var requestParams = {
        url: url,
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            "Accept": "application/json"
        },
        usePostMethod: true
    };

    // 发送请求到Java服务
    var response = Resources["ContentLoaderFunctions"].PostJSON(requestParams);

    if(response.success){
        res.success = true;
        res.pageNumbers = response.data;
        res.msg = response.msg || "页码提取成功";
    }else{
        res.success = false;
        res.msg = response.msg || "页码提取失败";
    }
} catch (error) {
    res.success = false;
    var msg = "[ExtractPdfPageNumbers]-操作失败，原因：" + error;
    res.msg = msg;

    // 如果提取失败，返回空的页码信息
    res.pageNumbers = {};
}

// 添加请求头信息到响应中
res.headers = requestParams ? requestParams.headers : {};

result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="GetAllCommitmentNodeSql"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    GetAllCommitmentNodeSql
 * @description   wanghq 2024年11月29日10:50:03
 * @implementation    {Script}
 *
 * @param    {NUMBER}    nodeId    
 *
 * @returns    {STRING}
 */
//查询分级承诺节点以及分级承诺下的所有B表
var sql = "select a.ID, " +
    "       a.PID, " +
    "       a.NAME, " +
    "       a.TYPE, " +
    "       b.COMMITMENT_TABLE_ID, " +
    "       b.QUALITY_REPORT_ID, " +
    "       b.SYNC_STATUS " +
    "from (select ID, PID, NAME, TYPE " +
    "      from LAUNCH_CONFIRM " +
    "      start with ID = " + nodeId + " " +
    "      connect by prior ID = PID " +
    "      order by LEVEL_NUM, SORT) a " +
    "         left join " +
    "     COMMITMENT_SYNC_RELATION b on a.ID = b.COMMITMENT_TABLE_ID ";

result = sql;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="LoadAitSyncTree"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**&#xD;
 * @definition    LoadAitSyncTree&#xD;
 * @description   加载AIT同步结构树 wanghq 2025年8月21日10:47:30&#xD;
 * @implementation    {Script}&#xD;
 *&#xD;
 * @param    {STRING}    modelName    型号名称&#xD;
 *&#xD;
 * @returns    {JSON}&#xD;
 */&#xD;
var res = {};&#xD;
&#xD;
try {&#xD;
    // 1. 根据型号名称查询结构树数据&#xD;
    var sql = ""&#xD;
        + "SELECT "&#xD;
        + "    ID, "&#xD;
        + "    PID, "&#xD;
        + "    TREEID, "&#xD;
        + "    TREE_ID, "&#xD;
        + "    NAME, "&#xD;
        + "    TEXT, "&#xD;
        + "    TYPE, "&#xD;
        + "    TABLE_NUM, "&#xD;
        + "    TABLE_NUM, "&#xD;
        + "    SORT, "&#xD;
        + "    NODESTATUS, "&#xD;
        + "    IS_ELECTRIC_TEST, "&#xD;
        + "    'true' AS ISPARENT "&#xD;
        + "FROM ( "&#xD;
        + "    SELECT * FROM ( "&#xD;
        + "        SELECT * FROM ait_tree "&#xD;
        + "        WHERE TYPE IN ('phase') "&#xD;
        + "        START WITH ID = ( "&#xD;
        + "            SELECT ID FROM ait_tree "&#xD;
        + "            WHERE PID = ( "&#xD;
        + "                SELECT ID FROM ait_tree "&#xD;
        + "                WHERE NAME = '" + modelName + "' "&#xD;
        + "                  AND TYPE = 'product' "&#xD;
        + "            ) "&#xD;
        + "              AND NAME = '正样阶段' "&#xD;
        + "              AND TYPE = 'phase' "&#xD;
        + "        ) "&#xD;
        + "        CONNECT BY PRIOR ID = PID "&#xD;
        + "        ORDER BY LEVEL_NUM, SORT "&#xD;
        + "    ) "&#xD;
        + "    UNION ALL "&#xD;
        + "    SELECT * FROM ait_tree "&#xD;
        + "    WHERE NAME = '" + modelName + "' "&#xD;
        + "      AND TYPE = 'product' "&#xD;
        + ")";&#xD;
&#xD;
    var data = Things['Thing.DB.Oracle'].RunQuery({ sql: sql }).ToJSON().rows;&#xD;
&#xD;
    if (data.length === 0) {&#xD;
        throw "找不到型号：" + modelName + " 的结构树数据";&#xD;
    }&#xD;
    res.success = true;&#xD;
    res.data = data;&#xD;
    res.msg = "结构树加载成功";&#xD;
&#xD;
} catch (error) {&#xD;
    res.success = false;&#xD;
    res.msg = "加载结构树失败，原因：" + error;&#xD;
    logger.error("LoadAitSyncTree失败：" + error);&#xD;
}&#xD;
&#xD;
result = res;&#xD;
</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="ProcessDirectoryUpdateTask"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    ProcessDirectoryUpdateTask
 * @description   datetime 2025年5月14日23:06:07
 * @implementation    {Script}
 *
 * @param    {STRING}    taskId    
 *
 * @returns    {JSON}
 */
var res = {};
try {
    // 查询任务信息
    var taskSql = "SELECT TASK_ID, NODE_ID, PARENT_ID, STATUS, USERNAME, PAGE_SIZE, PAGE_ORIENTATION " +
                  "FROM DIRECTORY_UPDATE_TASKS WHERE TASK_ID = '" + taskId + "'";
    var taskRows = Things['Thing.DB.Oracle'].RunQuery({sql: taskSql}).rows;

    if (taskRows.length === 0) {
        throw "任务不存在";
    }

    var task = taskRows[0];

    // 更新任务状态为处理中
    var updateStatusSql = "UPDATE DIRECTORY_UPDATE_TASKS SET STATUS = 'processing', UPDATED_TIME = SYSDATE " +
                          "WHERE TASK_ID = '" + task.TASK_ID + "'";
    Things['Thing.DB.Oracle'].RunCommand({sql: updateStatusSql});

    try {
        // 1. 生成PDF文档
        var pdfResult = generatePdf(task);

        if (!pdfResult.success) {
            throw pdfResult.msg;
        }

        // 更新任务中的PDF路径
        var updatePdfPathSql = "UPDATE DIRECTORY_UPDATE_TASKS SET PDF_PATH = '" + pdfResult.pdfPath + "', " +
                               "UPDATED_TIME = SYSDATE WHERE TASK_ID = '" + task.TASK_ID + "'";
        Things['Thing.DB.Oracle'].RunCommand({sql: updatePdfPathSql});

        // 2. 提取页码信息
        var pageNumbersResult = extractPageNumbers(task, pdfResult.pdfPath);

        if (!pageNumbersResult.success) {
            throw pageNumbersResult.msg;
        }

        // 3. 更新目录表中的页码信息
        var updateResult = updateDirectoryPageNumbers(task.NODE_ID, pageNumbersResult.pageNumbers);

        if (!updateResult.success) {
            throw updateResult.msg;
        }

        // 更新任务状态为完成
        var completeTaskSql = "UPDATE DIRECTORY_UPDATE_TASKS SET STATUS = 'completed', " +
                              "MESSAGE = '页码更新成功', UPDATED_TIME = SYSDATE " +
                              "WHERE TASK_ID = '" + task.TASK_ID + "'";
        Things['Thing.DB.Oracle'].RunCommand({sql: completeTaskSql});

        res.success = true;
        res.msg = "目录页码更新成功";
    } catch (processError) {
        // 更新任务状态为失败
        var failTaskSql = "UPDATE DIRECTORY_UPDATE_TASKS SET STATUS = 'failed', " +
                          "MESSAGE = '" + processError.toString().replace(/'/g, "''") + "', UPDATED_TIME = SYSDATE " +
                          "WHERE TASK_ID = '" + task.TASK_ID + "'";
        Things['Thing.DB.Oracle'].RunCommand({sql: failTaskSql});

        throw processError;
    }
} catch (error) {
    res.success = false;
    var msg = "[ProcessDirectoryUpdateTask]-操作失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}
result = res;

/**
 * 生成PDF文档
 * @param {Object} task 任务信息
 * @returns {Object} 生成结果
 */
function generatePdf(task) {
    try {
        // 调用现有的PDF导出服务
        var exportResult = Things['Thing.Fn.LaunchOnlineConfirm'].ExportMorePdf({
            postId: task.NODE_ID,
            postPId: task.PARENT_ID,
            pageSize: task.PAGE_SIZE,
            pageOrientation: task.PAGE_ORIENTATION
        });

        if (!exportResult.success) {
            return {
                success: false,
                msg: "生成PDF失败：" + exportResult.msg
            };
        }

        return {
            success: true,
            pdfPath: exportResult.data
        };
    } catch (error) {
        return {
            success: false,
            msg: "生成PDF异常：" + error
        };
    }
}

/**
 * 提取PDF中的页码信息
 * @param {Object} task 任务信息
 * @param {String} pdfPath PDF文件路径
 * @returns {Object} 提取结果
 */
function extractPageNumbers(task, pdfPath) {
    try {
        // 获取目录表所属A表下的所有B表
        var bTablesSql = "SELECT ID, NAME, TABLE_NUM FROM LAUNCH_CONFIRM WHERE PID = " + task.PARENT_ID +
                         " AND TYPE = 'b' ORDER BY SORT, ID";
        var bTablesRows = Things['Thing.DB.Oracle'].RunQuery({sql: bTablesSql}).ToJSON().rows;
        // 调用Java服务提取页码
        var extractResult = Things['Thing.Fn.LaunchOnlineConfirm'].ExtractPdfPageNumbers({
            pdfPath: pdfPath,
            bTables: bTablesRows
        });

        if (!extractResult.success) {
            return {
                success: false,
                msg: "提取页码失败：" + extractResult.msg
            };
        }

        return {
            success: true,
            pageNumbers: extractResult.pageNumbers
        };
    } catch (error) {
        return {
            success: false,
            msg: "提取页码异常：" + error
        };
    }
}

/**
 * 更新目录表中的页码信息
 * @param {Number} nodeId 目录节点ID
 * @param {Object} pageNumbers 页码信息
 * @returns {Object} 更新结果
 */
function updateDirectoryPageNumbers(nodeId, pageNumbers) {
    try {
        // 获取目录表数据
        var directorySql = "SELECT SAVE_DATA FROM LAUNCH_CONFIRM WHERE ID = " + nodeId;
        var directoryRows = Things['Thing.DB.Oracle'].RunQuery({sql: directorySql}).rows;

        if (directoryRows.length === 0) {
            return {
                success: false,
                msg: "目录表不存在"
            };
        }

        var saveData = JSON.parse(directoryRows[0].SAVE_DATA);
        var tableData = saveData.tableData;

        // 更新封面页码
        if (tableData.length &gt; 1) {
            tableData[1][2] = "1";
        }

        // 更新其他B表页码
        for (var i = 2; i &lt; tableData.length; i++) {
            var tableName = tableData[i][1];
            // 尝试多种可能的表名格式
            var pageNumber = null;

            // 直接匹配
            if (pageNumbers[tableName]) {
                pageNumber = pageNumbers[tableName];
            }
            // 尝试匹配卫星产品证明书/履历书
            else if (tableName.indexOf("卫星产品") &gt;= 0 &amp;&amp; (pageNumbers["卫星产品证明书"] || pageNumbers["卫星产品履历书"])) {
                pageNumber = pageNumbers["卫星产品证明书"] || pageNumbers["卫星产品履历书"];
            }
            // 尝试部分匹配
            else {
                for (var key in pageNumbers) {
                    if (key.indexOf(tableName) &gt;= 0 || tableName.indexOf(key) &gt;= 0) {
                        pageNumber = pageNumbers[key];
                        break;
                    }
                }
            }

            if (pageNumber) {
                tableData[i][2] = pageNumber.toString();
            } else {
                tableData[i][2] = "--";
            }
        }

        // 更新保存数据
        saveData.tableData = tableData;
        var tableDataStr = JSON.stringify(saveData);

        // 将英文单引号替换为′
        tableDataStr = tableDataStr.replace(/'/g, "′").replace(/？/g, "");

        // 转换表格数据为HTML
        var htmlData = Things['Thing.Util.HandsonTable'].TableData2Html({str: tableDataStr});

        // 转换为SQL CLOB格式
        var tableDataSql = Things['Thing.Util.HandsonTable'].StrToClobSql({str: tableDataStr});
        var htmlDataSql = Things['Thing.Util.HandsonTable'].StrToClobSql({str: htmlData});

        // 更新目录表数据
        var updateSql = "UPDATE LAUNCH_CONFIRM SET " +
                        "SAVE_TIME = SYSDATE, " +
                        "TABLE_STATUS = 'edit', " +
                        "SAVE_DATA = " + tableDataSql + ", " +
                        "HTML_DATA = " + htmlDataSql + " " +
                        "WHERE ID = " + nodeId;

        Things['Thing.DB.Oracle'].RunCommand({sql: updateSql});

        return {
            success: true,
            msg: "页码更新成功"
        };
    } catch (error) {
        return {
            success: false,
            msg: "更新页码异常：" + error
        };
    }
}</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="QueryCommitmentModel"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    QueryCommitmentModel
 * @description   查询分级承诺节的所属型号 wanghq 2024年11月29日11:03:00
 * @implementation    {Script}
 *
 * @param    {NUMBER}    nodeId    
 *
 * @returns    {STRING}
 */
var modelName = '';
var sql = 'select NAME from LAUNCH_CONFIRM where ID=(select PID from LAUNCH_CONFIRM where ID=' + nodeId + ')';
var rows = Things['Thing.DB.Oracle'].RunQuery({ sql: sql }).rows;
if (rows.length &gt; 0) {
    modelName = rows[0]['NAME'];
}
result = modelName;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="QueryMappingAitTableId"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    QueryMappingAitTableId
 * @description   查询映射关系表中的AIT表ID wanghq 2024年11月29日10:55:03
 * @implementation    {Script}
 *
 * @param    {STRING}    tableName    
 * @param    {STRING}    modelName    
 * @param    {INFOTABLE}    mappingData    
 *
 * @returns    {NUMBER}
 */
var aitTableId = -1;
var professionalName = '';
var processName = '';
var aitTableName = '';
for (var j = 0; j &lt; mappingData.rows.length; j++) {
    var mapping = mappingData.rows[j];
    if (mapping['COMMITMENT_TABLE'] == tableName) {
        professionalName = mapping['PROFESSIONAL_NODE'];
        processName = mapping['PROCESS_NODE'];
        aitTableName = mapping['CONFIRM_TABLE_NAME'];
        break;
    }
}
if (professionalName !== '' &amp;&amp; aitTableName !== '' &amp;&amp; modelName !== '') {
    var queryTreeIdSql = "select TREEID from TREE_ALL_VIEW where MODEL_NAME = '" + modelName + "' and PHASE_NAME = '正样阶段' and DIR_NAME = '" + professionalName + "'";
    if (processName) {
        queryTreeIdSql += " and LEAF_NAME = '" + processName + "' and NODETYPE='leaf'";
    } else {
        queryTreeIdSql += " and NODETYPE='dir'";
    }
    var queryTreeIdRows = Things['Thing.DB.Oracle'].RunQuery({ sql: queryTreeIdSql }).rows;
    if (queryTreeIdRows.length &gt; 0) {
        var treeId = queryTreeIdRows[0]['TREEID'];
        var queryAitTableIdSql = "select ID from QUALITY_REPORT where TREE_ID=" + treeId + " and NAME='" + aitTableName + "'";
        var queryAitTableIdRows = Things['Thing.DB.Oracle'].RunQuery({ sql: queryAitTableIdSql }).rows;
        if (queryAitTableIdRows.length &gt; 0) {
            aitTableId = queryAitTableIdRows[0]['ID'];
        }
    }
}
result = aitTableId;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="RecoverSync"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    RecoverSync
 * @description   恢复同步 wanghq 2024年11月29日11:16:49
 * @implementation    {Script}
 *
 * @param    {NUMBER}    nodeId    
 * @param    {STRING}    username    
 *
 * @returns    {JSON}
 */
var res = {};
try {

    var rs = me.UpdateSyncStatus({ nodeId: nodeId, username: username, syncStatus: 'ACTIVE' });
    res.success = rs.success;
    res.msg = rs.msg;
    if (rs.success) {
        res.msg = "恢复同步成功";
    }
} catch (error) {
    res.success = false;
    res.msg = "恢复同步失败，原因：" + error;
    logger.error(res.msg);
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="StopSync"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    StopSync
 * @description   终止同步 wanghq 2024年11月29日09:39:45
 * @implementation    {Script}
 *
 * @param    {NUMBER}    nodeId    
 * @param    {STRING}    username    
 *
 * @returns    {JSON}
 */
var res = {};
try {
    var rs = me.UpdateSyncStatus({ nodeId: nodeId, username: username, syncStatus: 'STOPPED' });
    res.success = rs.success;
    res.msg = rs.msg;
    if (rs.success) {
        res.msg = "终止同步成功";
    }
} catch (error) {
    res.success = false;
    res.msg = "终止同步失败，原因：" + error;
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="SyncCommitmentData"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    SyncCommitmentData
 * @description   wanghq 2024年11月28日10:49:23
 * @implementation    {Script}
 *
 * @param    {STRING}    nodeId    
 * @param    {STRING}    username        {"aspect.defaultValue":"adm"}
 *
 * @returns    {JSON}
 */
var res = {};
try {
    me.CreateSyncStatus({ nodeId: nodeId, username: username });
    //查询分级承诺节点以及分级承诺下的所有B表
    var allNode = Things['Thing.DB.Oracle'].RunQuery({ sql: me.GetAllCommitmentNodeSql({ nodeId: nodeId }) }).rows;

    var syncCount = 0;
    var syncResults = [];
    var totalCount = 0;

    //遍历allNode下的所有B表 根据映射关系表建立关系
    for (var i = 1; i &lt; allNode.length; i++) {
        var node = allNode[i];
        var tableId = node['ID'];
        var tableName = node['NAME'];
        var aitTableId = node['QUALITY_REPORT_ID'];
        var nodeSyncStatus = node['SYNC_STATUS'];

        totalCount++;

        var syncResult = {
            tableId: tableId,
            tableName: tableName,
            aitTableId: aitTableId,
            syncStatus: 'FAILED',
            errorMessage: ''
        };

        if (nodeSyncStatus == 'ACTIVE') {
            try {
                var aitSql = "select * from quality_report where ID=" + aitTableId;
                var aitRows = Things['Thing.DB.Oracle'].RunQuery({ sql: aitSql }).rows;
                if (aitRows.length &gt; 0) {
                    var aitData = aitRows[0];
                    var dataSource = aitData['DATA_SOURCE'];
                    var reportType = aitData['REPORT_TYPE'];
                    var dataType = aitData['DATA_TYPE'];
                    var aitTableName = aitData['NAME'];
                    var treeId = aitData['TREE_ID'];

                    syncResult.aitTableName = aitTableName;
                    syncResult.dataSource = dataSource;

                    // 获取AIT表的专业节点和过程节点信息
                    try {
                        var treeSql = "select DIR_NAME, LEAF_NAME from TREE_ALL_VIEW where TREEID = " + treeId;
                        var treeRows = Things['Thing.DB.Oracle'].RunQuery({ sql: treeSql }).rows;
                        if (treeRows.length &gt; 0) {
                            syncResult.aitProfessionalNode = treeRows[0]['DIR_NAME'] || '';
                            syncResult.aitProcessNode = treeRows[0]['LEAF_NAME'] || '';
                        } else {
                            syncResult.aitProfessionalNode = '';
                            syncResult.aitProcessNode = '';
                        }
                    } catch (treeError) {
                        syncResult.aitProfessionalNode = '';
                        syncResult.aitProcessNode = '';
                        logger.warn("获取AIT表节点信息失败：" + treeError);
                    }

                    // 调用公共的单表同步服务
                    var syncRes = Things['Thing.Fn.LaunchOnlineConfirm'].SyncSingleTable({
                        tableId: tableId,
                        aitTableId: aitTableId,
                        username: username
                    });

                    if (syncRes.success) {
                        //更新LAST_SYNC_TIME 和 LAST_SYNC_USER
                        var updateLastSyncSql = "update COMMITMENT_SYNC_RELATION set LAST_SYNC_TIME = SYSDATE, LAST_SYNC_USER = '" + username + "' where COMMITMENT_TABLE_ID = " + tableId;
                        Things['Thing.DB.Oracle'].RunCommand({ sql: updateLastSyncSql });

                        syncResult.syncStatus = 'SUCCESS';
                        syncCount++;
                    } else {
                        syncResult.errorMessage = syncRes.msg;
                    }
                } else {
                    syncResult.errorMessage = '找不到对应的AIT质量确认表';
                }
            } catch (tableError) {
                syncResult.errorMessage = '处理表时出错：' + tableError;
                logger.error("同步表失败：" + tableName + "，错误：" + tableError);
            }
        } else {
            syncResult.errorMessage = '同步状态不是ACTIVE，跳过同步';
        }

        syncResults.push(syncResult);
    }

    res.success = true;
    res.msg = "同步完成，成功：" + syncCount + "条，失败：" + (totalCount - syncCount) + "条";
    res.data = {
        syncResults: syncResults,
        syncCount: syncCount,
        totalCount: totalCount
    };
} catch (error) {
    res.success = false;
    res.msg = "同步失败，原因：" + error;
    logger.error("SyncCommitmentData失败：" + error);
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="SyncSingleTable"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**&#xD;
 * @definition    SyncSingleTable&#xD;
 * @description   单个表同步的核心逻辑 wanghq 2025年1月15日&#xD;
 * @implementation    {Script}&#xD;
 *&#xD;
 * @param    {STRING}    tableId           B表ID&#xD;
 * @param    {STRING}    aitTableId        AIT表ID&#xD;
 * @param    {STRING}    username          用户名 {"aspect.defaultValue":"adm"}&#xD;
 *&#xD;
 * @returns    {JSON}&#xD;
 */&#xD;
var res = {};&#xD;
&#xD;
try {&#xD;
    // 1. 查询AIT表信息&#xD;
    var aitSql = "select * from quality_report where ID=" + aitTableId;&#xD;
    var aitRows = Things['Thing.DB.Oracle'].RunQuery({ sql: aitSql }).rows;&#xD;
&#xD;
    if (aitRows.length === 0) {&#xD;
        throw '找不到对应的AIT质量确认表';&#xD;
    }&#xD;
&#xD;
    var aitData = aitRows[0];&#xD;
    var dataSource = aitData['DATA_SOURCE'];&#xD;
    var reportType = aitData['REPORT_TYPE'];&#xD;
    var dataType = aitData['DATA_TYPE'];&#xD;
    var aitTableName = aitData['NAME'];&#xD;
&#xD;
    // 2. 查询B表信息&#xD;
    var bTableSql = "select * from LAUNCH_CONFIRM where ID=" + tableId;&#xD;
    var bTableRows = Things['Thing.DB.Oracle'].RunQuery({ sql: bTableSql }).rows;&#xD;
&#xD;
    if (bTableRows.length === 0) {&#xD;
        throw '找不到对应的B表';&#xD;
    }&#xD;
&#xD;
    var bTableData = bTableRows[0];&#xD;
    var tableName = bTableData['NAME'];&#xD;
&#xD;
    // 3. 执行同步逻辑&#xD;
    if (dataSource == 'auto') {&#xD;
        var tableHeader = 0;&#xD;
        if (reportType == "actual" || reportType == 'summary') {&#xD;
            tableHeader = parseInt(Things['Thing.Fn.SecondTable'].QueryTableById({ tableId: dataType }).rows[0]['SECOND_DATA_ROWNUM'] || '1') - 1;&#xD;
        }&#xD;
&#xD;
        var postRes = Resources["ContentLoaderFunctions"].GetJSON({&#xD;
            url: Things['Thing.System'].GetFileHandleUrl() + "/launch/convert/auto/table?reportId=" + aitTableId&#xD;
        });&#xD;
&#xD;
        if (!postRes.success) {&#xD;
            throw '调用转换接口失败：' + (postRes.msg || '未知错误');&#xD;
        }&#xD;
&#xD;
        var tableData = postRes.data;&#xD;
        if (!tableData) {&#xD;
            throw '获取表格数据为空';&#xD;
        }&#xD;
&#xD;
        // 数据处理&#xD;
        tableData = tableData.replace(/'/g, "′").replace(/？/g, "");&#xD;
        var htmlData = Things['Thing.Util.HandsonTable'].TableData2Html({ str: tableData });&#xD;
        tableData = Things['Thing.Util.HandsonTable'].StrToClobSql({ str: tableData });&#xD;
        htmlData = Things['Thing.Util.HandsonTable'].StrToClobSql({ str: htmlData });&#xD;
&#xD;
        // 更新B表数据&#xD;
        var updateSql = "update LAUNCH_CONFIRM set TABLE_HEADER='" + tableHeader + "',SAVE_TIME= SYSDATE,SAVE_USER='" + username + "', SAVE_DATA = " + tableData + " ,TABLE_STATUS='sign',HTML_DATA = " + htmlData + " where id=" + tableId;&#xD;
        Things['Thing.DB.Oracle'].RunCommand({ sql: updateSql });&#xD;
&#xD;
        res.success = true;&#xD;
        res.msg = "B表【" + tableName + "】与AIT确认表【" + aitTableName + "】同步成功";&#xD;
    } else {&#xD;
        // 直接同步AIT数据&#xD;
        var updateSql = "update LAUNCH_CONFIRM set " +&#xD;
            "SAVE_DATA = (select SAVE_DATA from QUALITY_REPORT where ID = " + aitTableId + "), " +&#xD;
            "HTML_DATA = (select HTML_DATA from QUALITY_REPORT where ID = " + aitTableId + "), " +&#xD;
            "SAVE_TIME = SYSDATE, " +&#xD;
            "SAVE_USER = '" + username + "', " +&#xD;
            "TABLE_STATUS = 'sign', " +&#xD;
            "CURRENT_EDITOR = 'no_user' " +&#xD;
            "where ID = " + tableId;&#xD;
&#xD;
        Things['Thing.DB.Oracle'].RunCommand({ sql: updateSql });&#xD;
&#xD;
        res.success = true;&#xD;
        res.msg = "B表【" + tableName + "】与AIT确认表【" + aitTableName + "】同步成功";&#xD;
    }&#xD;
&#xD;
} catch (error) {&#xD;
    res.success = false;&#xD;
    res.msg = error;&#xD;
}&#xD;
&#xD;
result = res;&#xD;
</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="UpdateDirectory"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    UpdateDirectory
 * @description   UpdateDirectory datetime 2025年5月14日17:24:35
 * @implementation    {Script}
 *
 * @param    {NUMBER}    nodeId    
 * @param    {STRING}    username    
 * @param    {STRING}    pageSize    
 * @param    {STRING}    pageOrientation    
 *
 * @returns    {JSON}
 */
var res = {};
try {
    // 检查目录节点状态
    var dirNodeSql = "SELECT ID, PID, NAME, TYPE, TABLE_STATUS FROM LAUNCH_CONFIRM WHERE ID = " + nodeId;
    var dirNodeRows = Things['Thing.DB.Oracle'].RunQuery({sql: dirNodeSql}).rows;

    if (dirNodeRows.length === 0) {
        throw "目录节点不存在";
    }

    var dirNode = dirNodeRows[0];

    // 检查是否为B表节点且名称为"目录"
    if (dirNode.TYPE !== 'b' || dirNode.NAME !== '目录') {
        throw "指定节点不是目录表";
    }

    // 检查目录表是否已锁定
    if (dirNode.TABLE_STATUS === 'sign') {
        throw "目录表已锁定，无法更新";
    }

    // 获取父节点(A表)信息
    var parentNodeSql = "SELECT ID, NAME, TYPE FROM LAUNCH_CONFIRM WHERE ID = " + dirNode.PID;
    var parentNodeRows = Things['Thing.DB.Oracle'].RunQuery({sql: parentNodeSql}).rows;

    if (parentNodeRows.length === 0) {
        throw "找不到父节点";
    }

    var parentNode = parentNodeRows[0];

    // 检查父节点是否为A表
    if (parentNode.TYPE !== 'a') {
        throw "父节点不是A表";
    }

    // 检查父表名称是否含有"卫星产品履历书"或"卫星产品证明书"
    if (!parentNode.NAME.includes('卫星产品履历书') &amp;&amp; !parentNode.NAME.includes('卫星产品证明书')) {
        throw "父表不是卫星产品履历书或卫星产品证明书";
    }

    // 获取同一A表下的所有B表
    var bTablesSql = "SELECT ID, NAME, TABLE_NUM FROM LAUNCH_CONFIRM WHERE PID = " + parentNode.ID + " AND TYPE = 'b' ORDER BY SORT, ID";
    var bTablesRows = Things['Thing.DB.Oracle'].RunQuery({sql: bTablesSql}).rows;

    // 构建目录表格数据
    var tableData = [];
    // 添加表头行
    tableData.push(["序号", "名称", "页次"]);

    // 添加封面行
    tableData.push(["1", "封面", null]);

    // 添加其他B表
    for (var i = 0; i &lt; bTablesRows.length; i++) {
        var bTable = bTablesRows[i];
        // 不再跳过目录表自身

        // 序号从2开始，因为1已经是封面
        var index = i + 2;
        tableData.push([index.toString(), bTable.NAME, null]);
    }

    // 将表格数据转换为JSON字符串
    var tableDataJson = {
        tableData: tableData,
        merged: [],
        meta: [],
        colWidths: [100, 348, 100]  // 设置列宽
    };

    // 生成meta数据
    var metaData = [];
    for (var row = 0; row &lt; tableData.length; row++) {
        for (var col = 0; col &lt; tableData[row].length; col++) {
            // 为每个单元格创建meta对象
            metaData.push({
                "visualRow": row,
                "visualCol": col,
                "row": row,
                "col": col,
                "prop": col,
                "className": "htMiddle htCenter" // 设置垂直居中和水平居中
            });
        }
    }
    tableDataJson.meta = metaData;

    var tableDataStr = JSON.stringify(tableDataJson);

    // 将英文单引号替换为′
    tableDataStr = tableDataStr.replace(/'/g, "′").replace(/？/g, "");

    // 转换表格数据为HTML
    var htmlData = Things['Thing.Util.HandsonTable'].TableData2Html({str: tableDataStr});

    // 转换为SQL CLOB格式
    var tableDataSql = Things['Thing.Util.HandsonTable'].StrToClobSql({str: tableDataStr});
    var htmlDataSql = Things['Thing.Util.HandsonTable'].StrToClobSql({str: htmlData});

    // 更新目录表数据
    var updateSql = "UPDATE LAUNCH_CONFIRM SET " +
                    "SAVE_TIME = SYSDATE, " +
                    "SAVE_USER = '" + username + "', " +
                    "TABLE_STATUS = 'edit', " +
                    "CURRENT_EDITOR = 'no_user', " +
                    "TABLE_HEADER = '1', " +  // 设置表头行为1
                    "SAVE_DATA = " + tableDataSql + ", " +
                    "HTML_DATA = " + htmlDataSql + " " +
                    "WHERE ID = " + nodeId;

    Things['Thing.DB.Oracle'].RunCommand({sql: updateSql});

    // 创建异步任务，生成PDF并提取页码
    // 生成唯一的任务ID
    var taskId = "DIR_" + new Date().getTime() + "_" + nodeId;

    // 设置默认的PDF选项
    if (!pageSize) pageSize = "A4";
    if (!pageOrientation) pageOrientation = "landscape";

    // 创建任务记录
    var createTaskSql = "INSERT INTO DIRECTORY_UPDATE_TASKS " +
                        "(TASK_ID, NODE_ID, PARENT_ID, STATUS, USERNAME, PAGE_SIZE, PAGE_ORIENTATION, CREATED_TIME, UPDATED_TIME) " +
                        "VALUES ('" + taskId + "', " + nodeId + ", " + parentNode.ID + ", 'pending', '" +
                        username + "', '" + pageSize + "', '" + pageOrientation + "', SYSDATE, SYSDATE)";

    Things['Thing.DB.Oracle'].RunCommand({sql: createTaskSql});

    // 启动异步任务处理
    Things['Thing.Fn.LaunchOnlineConfirm'].ProcessDirectoryUpdateTask({
        taskId: taskId
    });

    res.success = true;
    res.msg = "目录更新成功，正在生成PDF并提取页码";
    res.taskId = taskId;
} catch (error) {
    res.success = false;
    var msg = "[UpdateDirectory]-操作失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="UpdateSyncStatus"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    UpdateSyncStatus
 * @description   更新同步状态表 wanghq 2024年11月29日11:13:22
 * @implementation    {Script}
 *
 * @param    {STRING}    nodeId    
 * @param    {STRING}    username    
 * @param    {STRING}    syncStatus        {"aspect.defaultValue":"ACTIVE"}
 *
 * @returns    {JSON}
 */
var res = {};
try {

    //查询分级承诺节点以及分级承诺下的所有B表
    var allNode = Things['Thing.DB.Oracle'].RunQuery({ sql: me.GetAllCommitmentNodeSql({ nodeId: nodeId }) }).rows;

    var modelName = me.QueryCommitmentModel({ nodeId: nodeId });

    //查询映射关系表 将B表与Ait的表建立关系
    var mappingData = Things['Thing.DB.Oracle'].RunQuery({ sql: 'select * from LAUNCH_CONFIRM_MAPPING' });


    //遍历allNode下的所有B表 根据映射关系表建立关系
    for (var i = 0; i &lt; allNode.length; i++) {
        var node = allNode[i];
        var tableId = node['ID'];
        var tableName = node['NAME'];
        var nodeSyncStatus = node['SYNC_STATUS'];
        var commandSql = '';
        if (nodeSyncStatus) {
            if (syncStatus == 'STOPPED') {
                //终止同步操作： 更新终止同步时间和终止同步人
                commandSql = "update COMMITMENT_SYNC_RELATION set SYNC_STATUS='" + syncStatus
                    + "', STOP_TIME=SYSDATE,STOP_USER='" + username + "' where COMMITMENT_TABLE_ID=" + tableId;
            } else {
                //恢复同步操作： 更新恢复同步时间和恢复同步人
                commandSql = "update COMMITMENT_SYNC_RELATION set SYNC_STATUS='" + syncStatus
                    + "', RECOVER_TIME=SYSDATE,RECOVER_USER='" + username + "' where COMMITMENT_TABLE_ID=" + tableId;
            }
        } else {
            var aitTableId = me.QueryMappingAitTableId({ tableName: tableName, modelName: modelName, mappingData: mappingData });
            if (syncStatus == 'STOPPED') {
                //终止同步操作： 插入终止同步时间和终止同步人
                commandSql = "insert into COMMITMENT_SYNC_RELATION (ID, COMMITMENT_TABLE_ID, QUALITY_REPORT_ID,SYNC_STATUS,STOP_TIME,STOP_USER,CREATE_TIME,CREATE_USER) " +
                    "values (SEQ_COMMITMENT_SYNC_RELATION.NEXTVAL, " + tableId + ", " + aitTableId + ", '" + syncStatus + "', SYSDATE, '" + username + "',SYSDATE, '" + username + "')";
            } else {
                //恢复同步操作： 插入恢复同步时间和恢复同步人
                commandSql = "insert into COMMITMENT_SYNC_RELATION (ID, COMMITMENT_TABLE_ID, QUALITY_REPORT_ID,SYNC_STATUS,RECOVER_TIME,RECOVER_USER,CREATE_TIME,CREATE_USER) " +
                    "values (SEQ_COMMITMENT_SYNC_RELATION.NEXTVAL, " + tableId + ", " + aitTableId + ", '" + syncStatus + "', SYSDATE, '" + username + "',SYSDATE, '" + username + "')";
            }
        }
        Things['Thing.DB.Oracle'].RunCommand({ sql: commandSql });
    }
    res.success = true;
} catch (error) {
    res.success = false;
    res.msg = "更新同步状态失败，原因：" + error;
    logger.error(res.msg);
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation></ServiceImplementations><Subscriptions/></ThingShape><PropertyBindings/><RemotePropertyBindings/><RemoteServiceBindings/><RemoteEventBindings/><AlertConfigurations/><ImplementedShapes/><ThingProperties><moduleName><Value>发射场确认</Value><Timestamp>2023-05-12T14:25:31.313+08:00</Timestamp><Quality>GOOD</Quality></moduleName><pageType><Value>launch</Value><Timestamp>2025-07-18T09:33:52.079+08:00</Timestamp><Quality>GOOD</Quality></pageType><photoSeqName><Value>LAUNCH_PHOTO_SEQ</Value><Timestamp>2025-07-23T10:56:15.110+08:00</Timestamp><Quality>GOOD</Quality></photoSeqName><photoTableName><Value>LAUNCH_PHOTO</Value><Timestamp>2022-06-23T11:16:44.116+08:00</Timestamp><Quality>GOOD</Quality></photoTableName><relationIdName><Value>LAUNCH_ID</Value><Timestamp>2022-06-23T11:16:50.203+08:00</Timestamp><Quality>GOOD</Quality></relationIdName><reqIdent><Value>online</Value><Timestamp>2023-09-13T21:56:35.633+08:00</Timestamp><Quality>GOOD</Quality></reqIdent><signSeqName><Value>LAUNCH_SIGN_SEQ</Value><Timestamp>2022-06-23T11:16:56.629+08:00</Timestamp><Quality>GOOD</Quality></signSeqName><signTableName><Value>LAUNCH_SIGN</Value><Timestamp>2022-06-23T11:17:01.479+08:00</Timestamp><Quality>GOOD</Quality></signTableName><tableName><Value>LAUNCH_CONFIRM</Value><Timestamp>2022-06-23T11:17:06.629+08:00</Timestamp><Quality>GOOD</Quality></tableName></ThingProperties></Thing></Things></Entities>