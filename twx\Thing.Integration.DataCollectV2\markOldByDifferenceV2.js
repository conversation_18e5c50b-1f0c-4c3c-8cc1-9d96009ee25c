/**
 * @definition    markOldByDifferenceV2
 * @description   差异置旧：仅将本批次未返回的 source_id 标记为 old  wanghq
 * @implementation    {Script}
 *
 * @param    {STRING}    tableName
 * @param    {NUMBER}    nodecode
 * @param    {STRING}    tableType
 * @param    {INFOTABLE} batchSourceIds   {"datashape":"StringList"}
 *
 * @returns    {NUMBER}
 */

// 说明：
// 1) XMLDATA_* 使用 SOURCE_ID 保存唯一键；
// 2) 结果表使用 ID 作为主键，且 XMLDATA_* 中 result_id 对应结果表；
// 为避免跨表 join，这里按 XMLDATA_* 做差异计算，再回写结果表 STATUS。

var count = 0;
// 收集当前批次的 source_id
var allow = {};
if (batchSourceIds && batchSourceIds.rows) {
	for (var i = 0; i < batchSourceIds.rows.length; i++) {
		var sid = batchSourceIds.rows[i].name;
		allow[sid] = true;
	}
}

// 找到该节点、该类型下已存在且不在本批次的业务数据
var xmldataTable = "XMLDATA_" + tableType;
var query = "select SOURCE_ID, RESULT_ID from " + xmldataTable + " where RESULT_ID in (select id from " + tableName + " where NODECODE='" + nodecode + "')";
var rs = Things['Thing.DB.Oracle'].RunQuery({ sql: query });
for (var j = 0; j < rs.rows.length; j++) {
	var sourceId = rs.rows[j].SOURCE_ID;
	if (!allow[sourceId]) {
		var rid = rs.rows[j].RESULT_ID;
		Things['Thing.DB.Oracle'].RunCommand({ sql: "update " + tableName + " set STATUS='old' where id=" + rid });
		count++;
	}
}

result = count;


