/**
 * AIT树公共模块
 * 提供AIT质量确认模块的树结构相关功能
 * 包含树初始化、节点处理、图标设置等共用方法
 */

// AIT树对象全局变量
var aitTreeObj;

/**
 * 处理AIT节点图标
 * @param {Array} datas 节点数据数组
 * @returns {Array} 处理后的节点数据数组
 */
function dealAitDataIcons(datas) {
    var imagePrefix = '../dataTree/';
    for (var i = 0; i < datas.length; i++) {
        var dtype = datas[i].TYPE;
        if (dtype === 'root') {
            datas[i].drag = false;
            datas[i].icon = imagePrefix + "images/root.png";
        } else if (dtype === 'folder') {
            datas[i].drag = false;
            datas[i].icon = imagePrefix + "images/folder.png";
        } else if (dtype === 'product') {
            datas[i].drag = false;
            datas[i].icon = imagePrefix + "images/卫星.png";
        } else if (dtype === 'phase') {
            datas[i].drag = false;
            if (datas[i].CODE === 'C') {
                datas[i].icon = imagePrefix + "images/phase.png";
            } else if (datas[i].CODE === 'Z') {
                datas[i].icon = imagePrefix + "images/phase_z.png";
            } else {
                datas[i].icon = imagePrefix + "images/phase.png";
            }
        } else if (dtype === 'dir') {
            datas[i].drag = false;
            datas[i].icon = imagePrefix + "images/dir.png";
        } else if (dtype === 'leaf') {
            datas[i].drag = false;
            var status = 'start';
            if (datas[i].NODESTATUS != undefined && datas[i].NODESTATUS != "undefined") {
                status = datas[i].NODESTATUS;
            }
            datas[i].icon = imagePrefix + "images/" + status + ".png";
            datas[i].childOuter = false;
        } else if (dtype === 'report') {
            datas[i].childOuter = false;
            datas[i].icon = imagePrefix + "images/report.png";
        } else if (dtype.indexOf('table') > -1) {
            datas[i].icon = imagePrefix + "images/table.png";
            datas[i].chkDisabled = false; // table类型节点可以选中
            if (dtype.indexOf('table_3') > -1) {
                datas[i].dropInner = false;
            } else {
                datas[i].childOuter = false;
            }
        } else {
            // 其他类型节点禁用复选框
            datas[i].chkDisabled = true;
        }
    }
    return datas;
}

/**
 * 处理节点前缀
 * @param {number} sort 排序号
 * @param {number} num 数字位数，默认为2
 * @returns {string} 格式化的前缀字符串
 */
function dealAitPrefix(sort, num) {
    if (!num) {
        num = 2;
    }
    var sortStr = sort + "";
    if (sortStr.length < num) {
        var temp = "";
        for (var i = 0; i < (num - sortStr.length); i++) {
            temp += "0";
        }
        sortStr = temp + sort;
    }
    return sortStr;
}

/**
 * 处理AIT节点数据名称
 * @param {Array} datas 节点数据数组
 * @returns {Array} 处理后的节点数据数组
 */
function dealAitDataNodeName(datas) {
    for (var i = 0; i < datas.length; i++) {
        var dtype = datas[i].TYPE;
        if (dtype === 'folder' || dtype === 'dir' || dtype === 'leaf') {
            datas[i].TEXT = dealAitPrefix(datas[i].SORT) + "-" + datas[i].NAME;
        } else if ((dtype === 'report' || dtype.indexOf('table') > -1) && datas[i].IS_ELECTRIC_TEST == 0) {
            var tableNum = datas[i].TABLE_NUM == "请添加序号" ? '<span style="color:red;">请添加序号</span>' : datas[i].TABLE_NUM;
            var tableName = datas[i].NAME == "请填写表名称" ? '<span style="color:red;">请填写表名称</span>' : datas[i].NAME;
            datas[i].TEXT = tableNum + ':' + tableName;
        } else {
            datas[i].TEXT = datas[i].NAME;
        }
    }
    return datas;
}

