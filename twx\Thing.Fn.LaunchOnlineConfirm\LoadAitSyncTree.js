/**
 * @definition    LoadAitSyncTree
 * @description   加载AIT同步结构树 wanghq 2025年8月21日10:47:30
 * @implementation    {Script}
 *
 * @param    {STRING}    modelName    型号名称
 *
 * @returns    {JSON}
 */
var res = {};

try {
    // 1. 根据型号名称查询结构树数据
    var sql = ""
        + "SELECT "
        + "    ID, "
        + "    PID, "
        + "    TREEID, "
        + "    TREE_ID, "
        + "    NAME, "
        + "    TEXT, "
        + "    TYPE, "
        + "    TABLE_NUM, "
        + "    TABLE_NUM, "
        + "    SORT, "
        + "    NODESTATUS, "
        + "    IS_ELECTRIC_TEST, "
        + "    'true' AS ISPARENT "
        + "FROM ( "
        + "    SELECT * FROM ( "
        + "        SELECT * FROM ait_tree "
        + "        WHERE TYPE IN ('phase') "
        + "        START WITH ID = ( "
        + "            SELECT ID FROM ait_tree "
        + "            WHERE PID = ( "
        + "                SELECT ID FROM ait_tree "
        + "                WHERE NAME = '" + modelName + "' "
        + "                  AND TYPE = 'product' "
        + "            ) "
        + "              AND NAME = '正样阶段' "
        + "              AND TYPE = 'phase' "
        + "        ) "
        + "        CONNECT BY PRIOR ID = PID "
        + "        ORDER BY LEVEL_NUM, SORT "
        + "    ) "
        + "    UNION ALL "
        + "    SELECT * FROM ait_tree "
        + "    WHERE NAME = '" + modelName + "' "
        + "      AND TYPE = 'product' "
        + ")";

    var data = Things['Thing.DB.Oracle'].RunQuery({ sql: sql }).ToJSON().rows;

    if (data.length === 0) {
        throw "找不到型号：" + modelName + " 的结构树数据";
    }
    res.success = true;
    res.data = data;
    res.msg = "结构树加载成功";

} catch (error) {
    res.success = false;
    res.msg = "加载结构树失败，原因：" + error;
    logger.error("LoadAitSyncTree失败：" + error);
}

result = res;
