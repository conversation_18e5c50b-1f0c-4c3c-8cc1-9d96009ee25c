/**
 * @definition    SyncSingleTable
 * @description   单个表同步的核心逻辑 wanghq 2025年1月15日
 * @implementation    {Script}
 *
 * @param    {STRING}    tableId           B表ID
 * @param    {STRING}    aitTableId        AIT表ID
 * @param    {STRING}    username          用户名 {"aspect.defaultValue":"adm"}
 *
 * @returns    {JSON}
 */
var res = {};

try {
    // 1. 查询AIT表信息
    var aitSql = "select * from quality_report where ID=" + aitTableId;
    var aitRows = Things['Thing.DB.Oracle'].RunQuery({ sql: aitSql }).rows;

    if (aitRows.length === 0) {
        throw '找不到对应的AIT质量确认表';
    }

    var aitData = aitRows[0];
    var dataSource = aitData['DATA_SOURCE'];
    var reportType = aitData['REPORT_TYPE'];
    var dataType = aitData['DATA_TYPE'];
    var aitTableName = aitData['NAME'];

    // 2. 查询B表信息
    var bTableSql = "select * from LAUNCH_CONFIRM where ID=" + tableId;
    var bTableRows = Things['Thing.DB.Oracle'].RunQuery({ sql: bTableSql }).rows;

    if (bTableRows.length === 0) {
        throw '找不到对应的B表';
    }

    var bTableData = bTableRows[0];
    var tableName = bTableData['NAME'];

    // 3. 执行同步逻辑
    if (dataSource == 'auto') {
        var tableHeader = 0;
        if (reportType == "actual" || reportType == 'summary') {
            tableHeader = parseInt(Things['Thing.Fn.SecondTable'].QueryTableById({ tableId: dataType }).rows[0]['SECOND_DATA_ROWNUM'] || '1') - 1;
        }

        var postRes = Resources["ContentLoaderFunctions"].GetJSON({
            url: Things['Thing.System'].GetFileHandleUrl() + "/launch/convert/auto/table?reportId=" + aitTableId
        });

        if (!postRes.success) {
            throw '调用转换接口失败：' + (postRes.msg || '未知错误');
        }

        var tableData = postRes.data;
        if (!tableData) {
            throw '获取表格数据为空';
        }

        // 数据处理
        tableData = tableData.replace(/'/g, "′").replace(/？/g, "");
        var htmlData = Things['Thing.Util.HandsonTable'].TableData2Html({ str: tableData });
        tableData = Things['Thing.Util.HandsonTable'].StrToClobSql({ str: tableData });
        htmlData = Things['Thing.Util.HandsonTable'].StrToClobSql({ str: htmlData });

        // 更新B表数据
        var updateSql = "update LAUNCH_CONFIRM set TABLE_HEADER='" + tableHeader + "',SAVE_TIME= SYSDATE,SAVE_USER='" + username + "', SAVE_DATA = " + tableData + " ,TABLE_STATUS='sign',HTML_DATA = " + htmlData + " where id=" + tableId;
        Things['Thing.DB.Oracle'].RunCommand({ sql: updateSql });

        res.success = true;
        res.msg = "B表【" + tableName + "】与AIT确认表【" + aitTableName + "】同步成功";
    } else {
        // 直接同步AIT数据
        var updateSql = "update LAUNCH_CONFIRM set " +
            "SAVE_DATA = (select SAVE_DATA from QUALITY_REPORT where ID = " + aitTableId + "), " +
            "HTML_DATA = (select HTML_DATA from QUALITY_REPORT where ID = " + aitTableId + "), " +
            "SAVE_TIME = SYSDATE, " +
            "SAVE_USER = '" + username + "', " +
            "TABLE_STATUS = 'sign', " +
            "CURRENT_EDITOR = 'no_user' " +
            "where ID = " + tableId;

        Things['Thing.DB.Oracle'].RunCommand({ sql: updateSql });

        res.success = true;
        res.msg = "B表【" + tableName + "】与AIT确认表【" + aitTableName + "】同步成功";
    }

} catch (error) {
    res.success = false;
    res.msg = error;
}

result = res;
