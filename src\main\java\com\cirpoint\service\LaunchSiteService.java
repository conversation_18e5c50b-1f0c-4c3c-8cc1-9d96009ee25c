package com.cirpoint.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.cirpoint.util.CommonUtil;
import com.cirpoint.util.Util;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
public class LaunchSiteService {

	private final TableService secondaryTertiaryTableService;

	@Autowired
	public LaunchSiteService(TableService tableService) {
		this.secondaryTertiaryTableService = tableService;
	}

	/**
	 * 导入分级承诺映射模板
	 *
	 * @param file     Excel文件
	 * @param username 导入用户
	 * @return 导入结果
	 */
	public JSONObject importMappingTemplate(MultipartFile file, String username) throws IOException {
		ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
		// 生成批次号
		String batchNo = System.currentTimeMillis() + "";

		// 读取Excel数据
		List<Map<String, Object>> rows = reader.readAll();

		// 校验数据
		if (rows.isEmpty()) {
			throw new IllegalArgumentException("Excel文件为空");
		}

		// 校验表头
		Map<String, Object> firstRow = rows.get(0);
		if (!firstRow.containsKey("分级承诺表") ||
				!firstRow.containsKey("专业节点") ||
				!firstRow.containsKey("过程节点") ||
				!firstRow.containsKey("确认表名称")) {
			throw new IllegalArgumentException("Excel表头不符合要求");
		}

		// 删除旧数据
		String deleteSql = "DELETE FROM LAUNCH_CONFIRM_MAPPING";
		Util.postCommandSql(deleteSql);

		// 插入新数据
		StringBuilder insertSql = new StringBuilder();
		insertSql.append("INSERT ALL ");

		for (Map<String, Object> row : rows) {
			String commitmentTable = String.valueOf(row.get("分级承诺表"));
			String professionalNode = String.valueOf(row.get("专业节点"));
			String processNode = row.get("过程节点") == null ? "" : String.valueOf(row.get("过程节点"));
			String confirmTableName = String.valueOf(row.get("确认表名称"));

			// 校验必填字段
			if (StrUtil.hasBlank(commitmentTable, professionalNode, confirmTableName)) {
				continue;
			}

			insertSql.append("INTO LAUNCH_CONFIRM_MAPPING (")
					.append("BATCH_NO, COMMITMENT_TABLE, PROFESSIONAL_NODE, PROCESS_NODE, ")
					.append("CONFIRM_TABLE_NAME, IMPORT_TIME, IMPORT_USER) VALUES ('")
					.append(batchNo).append("', '")
					.append(commitmentTable).append("', '")
					.append(professionalNode).append("', '")
					.append(processNode).append("', '")
					.append(confirmTableName).append("', ")
					.append("SYSDATE, '")
					.append(username).append("') ");
		}

		insertSql.append("SELECT 1 FROM DUAL");
		// 执行插入
		Util.postCommandSql(insertSql.toString());

		return JSONUtil.createObj().set("batchNo", batchNo);
	}

	/**
	 * 导出分级承诺映射数据到Excel
	 */
	public File exportMappingData() {
		String sql = "SELECT COMMITMENT_TABLE , " +
				"PROFESSIONAL_NODE , " +
				"PROCESS_NODE , " +
				"CONFIRM_TABLE_NAME  " +
				"FROM LAUNCH_CONFIRM_MAPPING";

		JSONArray dataList = Util.postQuerySql(sql);
		// 准备数据
		JSONArray data = new JSONArray();
		for (int i = 0; i < dataList.size(); i++) {
			JSONObject obj = dataList.getJSONObject(i);
			JSONArray row = new JSONArray();
			row.add(obj.getStr("COMMITMENT_TABLE", ""));
			row.add(obj.getStr("PROFESSIONAL_NODE", ""));
			row.add(obj.getStr("PROCESS_NODE", ""));
			row.add(obj.getStr("CONFIRM_TABLE_NAME", ""));
			data.add(row);
		}
		// 设置表头和列宽
		JSONArray headers = JSONUtil.parseArray(Arrays.asList("分级承诺表", "专业节点", "过程节点", "确认表名称"));
		JSONArray columnWidths = JSONUtil.parseArray(Arrays.asList(40, 40, 40, 50));
		return CommonUtil.createExcelFile("分级承诺映射数据", headers, data, columnWidths, 25);
	}


	/**
	 * 转换自动表格
	 *
	 * @param reportId 报告ID
	 * @return 表格数据JSON字符串
	 */
	public String convertAutoTable(String reportId) {
		String tableData = "";
		String sql = "select * from quality_report where ID='" + reportId + "'";
		JSONArray nodes = Util.postQuerySql(sql);
		if (!nodes.isEmpty()) {
			JSONObject node = nodes.getJSONObject(0);
			String dataSource = node.getStr("DATA_SOURCE");
			String reportType = node.getStr("REPORT_TYPE");
			String tableConfigId = node.getStr("DATA_TYPE");
			String treeId = node.getStr("TREE_ID");
			if ("auto".equals(dataSource)) {
				File file = null;
				int sheetIndex = 0;

				switch (reportType) {
					case "confirm":
						//确认表
						file = secondaryTertiaryTableService.exportQualityExcel(treeId, tableConfigId, "all");
						break;
					case "photo":
						//确认表 - 待实现
						break;
					case "summary":
						//汇总表
						file = secondaryTertiaryTableService.exportQualityExcel(treeId, tableConfigId, "all");
						sheetIndex = 1;
						break;
					case "actual":
						//实际汇总表
						file = secondaryTertiaryTableService.exportSecondExcel("-1", treeId, tableConfigId, "",
								"process", "{\"queryUser\":\"all\"}", "", 2);
						break;
				}

				if (file != null) {
					JSONObject res = sheetIndex > 0 ?
							Util.excel2HandsonTable(file.getAbsolutePath(), sheetIndex) :
							Util.excel2HandsonTable(file.getAbsolutePath());

					if (res.getBool("success")) {
						tableData = res.getJSONObject("data").toString();
					}
				}
			}
		}
		return tableData;
	}
}
