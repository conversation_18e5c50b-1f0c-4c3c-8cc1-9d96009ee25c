/**
 * @definition    getNowStrUnifiedV2
 * @description   统一时间源：优先调用平台时间服务，失败回退数据库时间  wanghq
 * @implementation    {Script}
 *
 * @returns    {STRING}
 */
var nowStr = '';
try {
    var timeUrl = Things["Thing.Fn.SystemDic"].getKeyByNames({ name: '统一时间服务URL', pname: '系统配置' });
    if (timeUrl && timeUrl !== '') {
        var p = { url: timeUrl, timeout: 5000 };
        var rs = Resources["ContentLoaderFunctions"].GetJSON(p);
        if (rs) {
            if (rs.now) { nowStr = String(rs.now); }
            else if (rs.time) { nowStr = String(rs.time); }
            else if (rs.T) { nowStr = String(rs.T); }
        }
    }
} catch (e) {}

if (nowStr === '') {
    nowStr = Things['Thing.Integration.DataCollectV2'].getNowStrV2();
}

result = nowStr;


