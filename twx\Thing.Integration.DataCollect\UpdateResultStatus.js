/**
 * @definition    UpdateResultStatus
 * @description   将状态变为old  wanghq 2025年5月22日9:55:52
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeid        {"aspect.defaultValue":"0.0"}
 *
 * @returns    {NOTHING}
 */

// 定义表名数组
var tables = [
	'DESIGN_DATA_RESULT',
	'CRAFT_DATA_RESULT',
	'PROCESS_CONTROL_RESULT',
	'QUALITY_CONTROL_RESULT'
];

// 构建 SQL 语句
var sqlStatements = [];
for (var i = 0; i < tables.length; i++) {
	var table = tables[i];
	if (treeid !== 0) {
		sqlStatements.push("update " + table + " set STATUS='old' where NODECODE=(select id from DATA_PACKAGE where REFTREEID=" + treeid + ")");
	} else {
		sqlStatements.push("update " + table + " set STATUS='old'");
	}
}

// 批量执行 SQL 语句
for (var j = 0; j < sqlStatements.length; j++) {
	Things['Thing.DB.Oracle'].RunCommand({
		sql: sqlStatements[j] /* STRING */
	});
}