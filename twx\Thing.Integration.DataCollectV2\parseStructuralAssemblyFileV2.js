/**
 * @definition    parseStructuralAssemblyFileV2
 * @description   结构装配Excel解析触发（复用旧Thing，可单独复跑） wanghq
 * @implementation    {Script}
 *
 * @param    {NUMBER}    ref_dpid
 * @param    {STRING}    data_id
 * @param    {NUMBER}    result_id
 * @param    {STRING}    product_id
 * @param    {STRING}    model
 * @param    {STRING}    thisFilePath
 *
 * @returns    {NOTHING}
 */
Things["Thing.Integration.DataCollect"].parseStructuralAssemblyFile({
	ref_dpid: ref_dpid,
	data_id: data_id,
	result_id: result_id,
	product_id: product_id,
	model: model,
	thisFilePath: thisFilePath
});


