var THING = 'Thing.Fn.LaunchOnlineConfirm';
var funcIdent = "launch";


function showTemplateLayer() {
	var fileFlag = false;
	layer.open({
		title: '分级承诺映射模板',
		type: 1,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		// fixed: false,
		maxmin: false,
		resize: false, //不允许拉伸
		area: ['450px', '300px'],
		content: '<div id="uploadContent" style="padding-top: 15px;padding-right: 15px;"></div>',
		btn: ['确认', '取消'],
		yes: function () {
			if (!fileFlag) {
				layer.alert('请选择需要导入的excel文件!', {
					icon: 2
				});
				return false;
			}
			uploadInst.config.url = fileHandlerUrl + '/launch/import/mapping?username=' + sessionStorage.getItem("username");
			$('#uploadStart').click();
		},
		btn2: function () {
			layer.closeAll();
		},
		success: function (layero, index, that) {
			var addTpl = '';
			addTpl = $("#uploadMappingHtml")[0].innerHTML;
			$("#uploadContent").append(addTpl);
		}
	});

	form.render(null, 'uploadForm');

	$("#downloadTpl").unbind('click').bind('click', function () {
		var loading;
		var url = fileHandlerUrl + "/launch/export/mapping";
		$.fileDownload(url, {
			httpMethod: 'POST',
			prepareCallback: function (url) {
				loading = layer.msg("正在下载...", {
					icon: 16,
					shade: 0.3,
					time: 0
				});
			},
			abortCallback: function (url) {
				layer.close(loading);
				layer.msg("下载异常！！");
			},
			successCallback: function (url) {
				layer.close(loading);
			},
			failCallback: function (html, url) {
				layer.close(loading);
				layer.msg("下载失败！！");
			}
		});
	});

	var loadIndex;
	var uploadInst = upload.render({
		elem: '#uploadChoice',
		url: '',
		auto: false,
		accept: 'file',
		field: 'file',
		exts: 'xlsx',
		bindAction: '#uploadStart',
		dataType: "json",
		choose: function (obj) {
			fileFlag = true;
			var files = obj.pushFile();
			var thisFile = obj.getChooseFiles();
			var thisFileIndex;
			var filename = '';
			for (var k in thisFile) {
				thisFileIndex = k;
				filename = thisFile[k].name;
			}

			for (var k in files) {
				if (thisFileIndex != k) {
					delete files[k];
				}
			}
			$("#selectedFile").show();
			$("#selectedFileName").text(filename);
		},
		before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
			loadIndex = layer.load(); //上传loading
		},
		done: function (res, index, upload) {
			layer.close(loadIndex);
			if (res.success) {
				layer.closeAll();
				layer.msg('导入成功！');
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		}
	});
}


// 同步数据
function syncCommitmentData(node) {
	// 保留确认框，但优化确认内容
	layer.confirm('确定要同步分级承诺书数据吗？<br><span style="color: #666; font-size: 12px;">同步将处理当前节点下的所有B表数据</span>', {
		title: '数据同步确认',
		btn: ['开始同步', '取消'],
		icon: 3,
		skin: 'layui-layer-molv'
	}, function () {
		// 用户确认后开始同步
		var loading = layer.load(1, {
			shade: [0.1, '#fff']
		});

		// 显示带文字的loading提示
		var loadingText = layer.msg('正在同步分级承诺数据，请稍候...', {
			icon: 16,
			shade: 0.3,
			time: 0 // 不自动关闭
		});

		// 调用后端同步接口
		twxAjax(THING, "SyncCommitmentData", {
			nodeId: node.ID,
			username: sessionStorage.getItem("username")
		}, true, function (res) {
			layer.close(loading);
			layer.close(loadingText);
			if (res.success) {
				// 显示同步结果详情
				showCommitmentSyncResultLayer(res.data);
				reloadTree(node.ID);
			} else {
				layer.alert(res.msg || '同步失败');
			}
		}, function () {
			layer.close(loading);
			layer.close(loadingText);
			layer.alert('同步失败');
		});
	});
}

// 终止同步
function stopCommitmentSync(node) {
	layer.confirm('确定要终止同步吗？', {
		btn: ['确定', '取消']
	}, function () {
		var loading = layer.load(1, {
			shade: [0.1, '#fff']
		});

		// 调用后端终止同步接口
		twxAjax(THING, "StopSync", {
			nodeId: node.ID,
			username: sessionStorage.getItem("username")
		}, true, function (res) {
			layer.close(loading);
			if (res.success) {
				layer.msg('已终止同步');
				reloadTree(node.PID, node.ID);
			} else {
				layer.alert(res.msg || '终止同步失败');
			}
		}, function () {
			layer.close(loading);
			layer.alert('终止同步失败');
		});
	});
}

// 恢复同步
function recoverCommitmentSync(node) {
	layer.confirm('确定要恢复同步吗？', {
		btn: ['确定', '取消']
	}, function () {
		var loading = layer.load(1, {
			shade: [0.1, '#fff']
		});

		// 调用后端恢复同步接口
		twxAjax(THING, "RecoverSync", {
			nodeId: node.ID,
			username: sessionStorage.getItem("username")
		}, true, function (res) {
			layer.close(loading);
			if (res.success) {
				layer.msg('已恢复同步');
				reloadTree(node.PID, node.ID);
			} else {
				layer.alert(res.msg || '恢复同步失败');
			}
		}, function () {
			layer.close(loading);
			layer.alert('恢复同步失败');
		});
	});
}


// 显示分级承诺同步结果反馈弹框 - 参照AIT同步结果展示方式
function showCommitmentSyncResultLayer(syncResultData) {
	if (!syncResultData || !syncResultData.syncResults) {
		layer.alert('同步结果数据为空', {
			icon: 2
		});
		return;
	}

	var resultList = syncResultData.syncResults || [];
	var syncCount = syncResultData.syncCount || 0;
	var totalCount = syncResultData.totalCount || 0;
	var failedCount = totalCount - syncCount;

	// 构建统计信息HTML
	var statisticsHtml = '<div style="padding: 15px; background-color: #f8f9fa; border-bottom: 1px solid #dee2e6;">' +
		'<div style="display: flex; gap: 20px;">' +
		'<span style="color: #28a745;"><strong>成功：' + syncCount + '条</strong></span>' +
		'<span style="color: #dc3545;"><strong>失败：' + failedCount + '条</strong></span>' +
		'<span style="color: #6c757d;"><strong>总计：' + totalCount + '条</strong></span>' +
		'</div>' +
		'</div>';

	// 使用layer.open创建弹框
	layer.open({
		type: 1,
		title: '分级承诺同步结果详情',
		content: '<div id="commitmentSyncResultContent" style="padding: 0;">' +
			statisticsHtml +
			'<div><table id="commitmentSyncResultTable"></table></div>' +
			'</div>',
		area: ['1500px', '700px'],
		success: function () {
			// 渲染同步结果表格
			renderCommitmentSyncResultTable(resultList);
		}
	});
}

// 渲染分级承诺同步结果表格 - 参照AIT同步结果表格方式
function renderCommitmentSyncResultTable(resultList) {
	// 定义表格列配置
	var tableCols = [[
		{ title: '序号', type: 'numbers', width: 60, align: 'center' },
		{ field: 'tableName', title: '分级承诺B表', width: 200, align: 'center' },
		{ field: 'aitProfessionalNode', title: 'AIT专业节点', width: 150, align: 'center' },
		{ field: 'aitProcessNode', title: 'AIT过程节点', width: 150, align: 'center' },
		{ field: 'aitTableName', title: 'AIT质量确认表', width: 200, align: 'center' },
		{
			field: 'dataSource', title: '数据源类型', width: 120, align: 'center',
			templet: function (d) {
				var dataSourceText = '';
				var dataSourceColor = '';
				if (d.dataSource === 'auto') {
					dataSourceText = '自动转换';
					dataSourceColor = '#007bff';
				} else {
					dataSourceText = '直接同步';
					dataSourceColor = '#28a745';
				}
				return '<span style="color: ' + dataSourceColor + ';">' + dataSourceText + '</span>';
			}
		},
		{
			field: 'syncStatus',
			title: '同步状态',
			width: 100,
			align: 'center',
			templet: function (d) {
				var statusClass = '';
				var statusIcon = '';
				var statusText = '';

				if (d.syncStatus === 'SUCCESS') {
					statusClass = 'color: #28a745; font-weight: bold;';
					statusIcon = '✓ ';
					statusText = '成功';
				} else if (d.syncStatus === 'FAILED') {
					statusClass = 'color: #dc3545; font-weight: bold;';
					statusIcon = '✗ ';
					statusText = '失败';
				} else {
					statusClass = 'color: #6c757d; font-weight: bold;';
					statusIcon = '? ';
					statusText = '未知';
				}

				return '<span style="' + statusClass + '">' + statusIcon + statusText + '</span>';
			}
		},
		{
			field: 'errorMessage',
			title: '失败原因',
			align: 'left',
			templet: function (d) {
				if (d.errorMessage && d.errorMessage !== '') {
					return '<span style="color: #dc3545;">' + d.errorMessage + '</span>';
				} else {
					return '<span style="color: #6c757d;">-</span>';
				}
			}
		}
	]];

	// 渲染表格
	table.render({
		elem: '#commitmentSyncResultTable',
		data: resultList,
		cols: tableCols,
		page: false,
		height: 596,
		even: true
	});
}

// 同步AIT确认表功能
function syncAitConfirmTable(currentBNode) {
	// 获取当前B表节点的表序号和表名称
	var tableNum = currentBNode.TABLE_NUM || '';
	var tableName = currentBNode.NAME || '';

	// 获取当前型号信息
	var parentNode = getParentNode(currentBNode);
	var grandParentNode = getParentNode(parentNode);
	var modelName = grandParentNode ? grandParentNode.NAME : '';

	var layerIndex = layer.open({
		title: '同步AIT确认表',
		type: 1,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		area: ['850px', '800px'],
		content: [
			'<div id="syncAitContent" style="padding: 15px;">',
			'  <div class="layui-row">',
			'    <div class="layui-col-md5">',
			'      <div class="layui-card">',
			'        <div class="layui-card-header">',
			'          <strong>当前B表信息</strong>',
			'        </div>',
			'        <div class="layui-card-body" style="height: 620px; padding-left: 0px;">',
			'          <div class="layui-form-item">',
			'            <label class="layui-form-label" style="width: 80px;padding-left: 0px;">表序号:</label>',
			'            <div class="layui-input-block" style="margin-left: 80px;">',
			'              <input type="text" readonly class="layui-input" value="', tableNum, '">',
			'            </div>',
			'          </div>',
			'          <div class="layui-form-item">',
			'            <label class="layui-form-label" style="width: 80px;padding-left: 0px;">表名称:</label>',
			'            <div class="layui-input-block" style="margin-left: 80px;">',
			'              <input type="text" readonly class="layui-input" value="', tableName, '">',
			'            </div>',
			'          </div>',
			'        </div>',
			'      </div>',
			'    </div>',
			'    <div class="layui-col-md7">',
			'      <div class="layui-card">',
			'        <div class="layui-card-header">',
			'          <strong>选择目标AIT确认表</strong>',
			'        </div>',
			'        <div class="layui-card-body">',
			'          <div id="aitTreeContainer" style="border: 1px solid #e2e2e2; padding: 10px; height: 600px; overflow-y: auto;">',
			'            <ul id="aitSyncTree" class="ztree"></ul>',
			'          </div>',
			'        </div>',
			'      </div>',
			'    </div>',
			'  </div>',
			'</div>'
		].join(''),
		btn: ['开始同步', '取消'],
		yes: function () {
			// 获取选中的AIT表节点 - 调用公共模块
			var selectedNode = getSelectedAitNode();
			if (!selectedNode) {
				layer.alert('请选择一个AIT确认表节点！', { icon: 2 });
				return false;
			}
			// 执行同步操作
			performSingleTableSync(currentBNode, selectedNode);
		},
		btn2: function () {
			layer.close(layerIndex);
		},
		success: function () {
			// 初始化AIT结构树 - 调用公共模块
			initAitSyncTree(modelName);
		}
	});
}

// 执行单个B表节点的同步操作
function performSingleTableSync(currentBNode, selectedAitNode) {
	layer.confirm('确定要将AIT确认表数据同步到当前B表吗？', {
		title: '确认同步',
		icon: 3
	}, function () {
		var loading = layer.load(1, {
			shade: [0.1, '#fff']
		});

		// 显示同步提示
		var loadingText = layer.msg('正在同步数据，请稍候...', {
			icon: 16,
			shade: 0.3,
			time: 0
		});

		// 调用后端单个B表同步接口
		twxAjax(THING, "SyncSingleTable", {
			tableId: currentBNode.ID,
			aitTableId: selectedAitNode.ID,
			username: sessionStorage.getItem("username")
		}, true, function (res) {
			layer.close(loading);
			layer.close(loadingText);
			if (res.success) {
				// 刷新树结构
				reloadTree(currentBNode.PID, currentBNode.ID);
				// 关闭弹窗
				layer.closeAll();
				layer.msg(res.msg);
			} else {
				layer.alert(res.msg || '同步失败', {
					icon: 2
				});
			}
		}, function () {
			layer.close(loading);
			layer.close(loadingText);
			layer.alert('同步失败');
		});
	});
}

/**
 * 初始化AIT同步树
 * @param {string} modelName 型号名称，用于定位和展开对应节点
 */
function initAitSyncTree(modelName) {
	var aitTreeSetting = {
		view: {
			dblClickExpand: false, //双击节点时，是否自动展开父节点的标识
			showLine: true, //是否显示节点之间的连线
			fontCss: {
				'color': 'black'
			}, //字体样式函数
			selectedMulti: false, //设置是否允许同时选中多个节点,
			txtSelectedEnable: true,
			showTitle: true,
			nameIsHTML: true
		},
		async: {
			enable: true,
			url: getTreeUrl("Thing.Fn.QualityReport", "AsyncQueryTree", ""),
			type: "post",
			autoParam: ["ID"],
			contentType: "application/json;charset=utf-8",
			dataType: 'json',
			dataFilter: function (treeId, parentNode, res) {
				if (res.success) {
					var datas = res.data;
					if (datas.length > 0) {
						// 为非table类型的节点设置nocheck属性，隐藏单选框
						for (var i = 0; i < datas.length; i++) {
							if (datas[i].TYPE !== "table") {
								datas[i].nocheck = true;
							}
						}
						datas = dealAitDataIcons(datas);
						datas = dealAitDataNodeName(datas);
					}
					return datas;
				} else {
					layer.alert(res.msg);
				}
			}
		},
		check: {
			chkboxType: {
				"Y": "ps",
				"N": "ps"
			},
			chkStyle: "radio", //复选框类型
			enable: true, //每个节点上是否显示 CheckBox
			chkDisabledInherit: true,
			chkboxType: { "Y": "ps", "N": "ps" }
		},
		data: {
			simpleData: { //简单数据模式
				enable: true,
				idKey: "ID",
				pIdKey: "PID",
				rootPId: 0
			},
			key: {
				name: 'TEXT',
				title: '',
				isParent: "ISPARENT"
			}
		},
		callback: {
			beforeCheck: function (treeId, treeNode) {
				// 只允许table类型的节点被选中
				return treeNode.TYPE === "table";
			},
			onClick: function (event, treeId, treeNode) {
				// 点击节点时自动选中
				if (treeNode.TYPE === "table") {
					aitTreeObj.checkNode(treeNode, !treeNode.checked, true);
				}
			},
			onCheck: function (event, treeId, treeNode) {
				// 勾选时只允许选择table类型的节点
				if (treeNode.TYPE === "table") {
					// 取消其他节点的选中状态
					var checkedNodes = aitTreeObj.getCheckedNodes(true);
					for (var i = 0; i < checkedNodes.length; i++) {
						if (checkedNodes[i].ID !== treeNode.ID && checkedNodes[i].TYPE === "table") {
							aitTreeObj.checkNode(checkedNodes[i], false, true);
						}
					}
				} else {
					// 非table节点不允许选中
					aitTreeObj.checkNode(treeNode, false, true);
				}
			},
			onAsyncSuccess: function (event, treeId, treeNode, msg) {
				// 自动展开正样阶段节点
				if (treeNode && treeNode.NAME === modelName) {
					var phaseNode = aitTreeObj.getNodeByParam("NAME", "正样阶段", treeNode);
					if (phaseNode) {
						aitTreeObj.expandNode(phaseNode, true, false, true);
					}
				}
			}
		}
	};

	// 调用新的服务加载结构树数据
	twxAjax(THING, "LoadAitSyncTree", {
		modelName: modelName
	}, false, function (res) {
		if (res.success) {
			var treeData = res.data;
			// 为非table类型的节点设置nocheck属性，隐藏单选框
			for (var i = 0; i < treeData.length; i++) {
				if (treeData[i].TYPE !== "table") {
					treeData[i].nocheck = true;
				}
			}
			treeData = dealAitDataIcons(treeData);
			treeData = dealAitDataNodeName(treeData);
			aitTreeObj = $.fn.zTree.init($("#aitSyncTree"), aitTreeSetting, treeData);

			// 自动展开正样阶段节点
			setTimeout(function () {
				var rootNodes = aitTreeObj.getNodes();
				for (var i = 0; i < rootNodes.length; i++) {
					aitTreeObj.expandNode(rootNodes[i], true, false, true);
					var phaseNodes = aitTreeObj.getNodesByParam("TYPE", "phase", rootNodes[i]);
					for (var j = 0; j < phaseNodes.length; j++) {
						if (phaseNodes[j].TEXT === "正样阶段") {
							aitTreeObj.expandNode(phaseNodes[j], true, false, true);
						}
					}
				}
			}, 100);
		} else {
			layer.alert("加载结构树失败：" + res.msg);
		}
	}, function () {
		layer.alert("加载结构树失败");
	});
}

/**
 * 获取选中的AIT表节点
 * @returns {Object|null} 选中的节点对象，包含ID和NAME属性，如果没有选中则返回null
 */
function getSelectedAitNode() {
	if (aitTreeObj) {
		var checkedNodes = aitTreeObj.getCheckedNodes(true);
		if (checkedNodes.length > 0) {
			var selectedNode = checkedNodes[0];
			// 只允许选择table类型的节点
			if (selectedNode.TYPE === "table") {
				return {
					ID: selectedNode.ID,  // 返回节点ID
					NAME: selectedNode.NAME    // 返回节点名称
				};
			}
		}
	}
	return null;
}