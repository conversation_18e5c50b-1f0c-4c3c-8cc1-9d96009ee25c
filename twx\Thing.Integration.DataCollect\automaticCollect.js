/**
 * @definition    automaticCollect
 * @description   自动采集  wanghq 2025年5月22日9:55:52
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeid        {"aspect.defaultValue":"0.0"}
 *
 * @returns    {STRING}
 */
var startTime = new Date().getTime();
//获取项目清单接口路径
var reqUrl = Things["Thing.Fn.SystemDic"].getKeyByNames({
	name: 'MES项目清单接口路径',
	pname: '系统配置'
});
var rsLen = 0; //成功数据条数
var result = '';
//同步之前将之前的数据状态变为old
me.UpdateResultStatus({
	treeid: treeid /* NUMBER */
});
try {
	if (reqUrl !== '') {
		//获取所有自动采集并且状态运行的策划构建数据
		var sql = "select a.*,b.REFTREEID from alldatalistview a,DATA_PACKAGE b where a.NODECODE=b.ID and a.gathering_method='自动采集' and a.collectstatus='1'";
		if (treeid !== 0) {
			sql += " and b.REFTREEID=" + treeid;
		}
		var allWaitToSyncTable = Things['Thing.DB.Oracle'].RunQuery({
			sql: sql
		});
		var allCount = allWaitToSyncTable.rows.length;
		for (var x = 0; x < allCount; x++) {
			var row = allWaitToSyncTable.rows[x];
			//xml请求参数
			var content = "";
			//过程节点
			var processCode = row.REFTREEID;
			//文件类型
			var type = row.FILE_TYPE;
			//四个类别  设计、工艺、过程、质量
			var category = row.TABLETYPE;
			//表名称
			var tableType = '';
			if (category == "设计") {
				tableType = "DESIGN_DATA_RESULT";
			} else if (category == "工艺") {
				tableType = "CRAFT_DATA_RESULT";
			} else if (category == "质量综合") {
				tableType = "QUALITY_CONTROL_RESULT";
			} else if (category == "过程控制") {
				tableType = "PROCESS_CONTROL_RESULT";
			}

			//请求发送的type值
			var entype = Things["Thing.Fn.SystemDic"].getFileTypeByName({
				name: type,
				type: '自动采集清单'
			});
			//entype=='' 没有该文件类型的值 跳过
			if (entype === '') {
				continue;
			}
			//if (type != "跟踪卡") {
			//	continue;
			//}
			//if (processCode != 58) {
			//	continue;
			//}
			content =
				'<?xml version="1.0" encoding="utf-8"?>\
			    <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">\
			      <soap:Body>\
			        <GetProcessListInfo xmlns="http://tempuri.org/">\
			            <xmlSend>&lt;Request&gt;&lt;ProcessCode&gt;' +
				processCode + '&lt;/ProcessCode&gt;&lt;Category&gt;' + category + '&lt;/Category&gt;&lt;Type&gt;' +
				entype +
				'&lt;/Type&gt;&lt;/Request&gt;</xmlSend>\
			        </GetProcessListInfo>\
			      </soap:Body>\
			    </soap:Envelope>';

			//PostXML所需参数
			var params = {
				headers: {
					"Content-Type": "text/xml; charset=utf-8"
				},
				url: reqUrl,
				timeout: 6000000,
				content: content
			};
			var resultXml = Resources["ContentLoaderFunctions"].PostXML(params);
			//解析返回的xml
			var contentXml = resultXml.*::Body.*::GetProcessListInfoResponse;

			resultXml = String(contentXml.*::GetProcessListInfoResult);
			resultXml = resultXml.substring(resultXml.indexOf("<Response"), resultXml.indexOf("</Response>") + 11);

			//添加集成日志
			//Things["Thing.Integration.LogUtil"].createLog({
			//	fileName: processCode + "_" + category + "_" + entype /* STRING */ ,
			//	rsContent: resultXml /* STRING */ ,
			//	type: "项目清单" /* STRING */
			//});

			//结果集的xml
			var xml = new XML(resultXml);
			//文件名称赋值列名
			var fileNameClo = me.getTagFileName({
				type: entype
			});
			//处理结果集
			for each(var tag in xml.Record) {
				var source_id = tag.Id;
				if (source_id == '') {
					continue;
				}
				var productId = tag.ProductId;
				// if (productId == '') {
				// 	continue;
				// }
				//文件处理
				var pdfFileName = "";
				var pdfFilePath = "";
				var pdfFileFormat = "";
				var excelFileName = "";
				var excelFilePath = "";
				var excelFileFormat = "";
				var fileName = "";
				var filePath = "";
				var fileFormat = "";
				if (entype == "CheckCard") {
					var pdfjson = me.downloadFileTest({
						fileName: "",
						filePath: "",
						url: tag.PDFDownLoadURL
					});
					if (pdfjson.success) {
						pdfFileName = pdfjson['fileName'];
						pdfFilePath = pdfjson['filePath'];
						pdfFileFormat = pdfjson['fileFormat'];
					}
					var exceljson = me.downloadFileTest({
						fileName: "",
						filePath: "",
						url: tag.ExcelDownLoadURL
					});
					if (exceljson.success) {
						excelFileName = exceljson['fileName'];
						excelFilePath = exceljson['filePath'];
						excelFileFormat = exceljson['fileFormat'];
					}
				} else if (entype == "Photo") {
					var durl = tag.DownLoadURL;
					if (durl != '') {
						filePath = durl;
						var b = durl.substring(0, durl.indexOf('_'));
						fileFormat = b.substring(b.lastIndexOf('.') + 1);
						if (fileFormat != 'jpg' && fileFormat != 'JPG' && fileFormat != 'png' && fileFormat !=
							'PNG' && fileFormat !=
							'gif' &&
							fileFormat != 'GIF' && fileFormat != 'bpm' && fileFormat != 'BPM' && fileFormat !=
							'mp4' && fileFormat != 'MP4' &&
							fileFormat != 'rmvb' && fileFormat != 'RMVB' && fileFormat != 'flv' && fileFormat !=
							'FLV' && fileFormat !=
							'mov' &&
							fileFormat != 'MOV' && fileFormat != 'avi' && fileFormat != 'AVI') {
							fileFormat = "";
						}
					}
				} else if (entype == "TechCard" || entype == "TechProblem" || entype == "ScrapNotice" || entype ==
					"ProductSubmit" || entype == "UndeliveredProduct" || entype == "UniversalTrackingCard") {
					var json = me.downloadFileTest({
						fileName: "",
						filePath: "",
						url: tag.DownLoadURL
					});
					if (json.success) {
						fileName = json['fileName'];
						filePath = json['filePath'];
						fileFormat = json['fileFormat'];
					}
				}
				source_id = source_id + '_' + tableType;
				//查询该条返回数据是否存在
				var existSql = "select * from XMLDATA_" + entype + " where source_id='" + source_id + "'";
				var rs = Things["Thing.DB.Oracle"].RunQuery({
					sql: existSql
				});
				var len = rs.rows.length;
				var resultId = 0;
				var pdf_resultId = 0;
				var excel_resultId = 0;
				if (len == 0) { //不存在为新增
					if (entype == "CheckCard") {
						if (pdfFileFormat == '') {
							pdfFileFormat = 'pdf';
						}
						pdf_resultId = me.addDataToResultTable({
							type: tableType,
							NODECODE: row.NODECODE,
							NODENAME: row.NODECODE,
							FILE_NUMBER: "",
							FILE_NAME: tag[fileNameClo],
							FILE_TYPE: type,
							GATHERING_METHOD: "自动采集",
							SOURCE_SYSTEM: "MES",
							DELIVERY_STATE: row.DELIVERY_STATE,
							SECURITY_LEVEL: tag.SecurityLevel,
							FILEPATH: pdfFilePath,
							FILENAME: pdfFileName,
							STATE_CHECK: "未确认",
							STATUS: 'new',
							productId: productId,
							FILE_FORMAT: pdfFileFormat
						});
						if (excelFileFormat == '') {
							excelFileFormat = 'xlsx';
						}
						excel_resultId = me.addDataToResultTable({
							type: tableType,
							NODECODE: row.NODECODE,
							NODENAME: row.NODECODE,
							FILE_NUMBER: "",
							FILE_NAME: tag[fileNameClo],
							FILE_TYPE: type,
							GATHERING_METHOD: "自动采集",
							SOURCE_SYSTEM: "MES",
							DELIVERY_STATE: row.DELIVERY_STATE,
							SECURITY_LEVEL: tag.SecurityLevel,
							FILEPATH: excelFilePath,
							FILENAME: excelFileName,
							STATE_CHECK: "未确认",
							STATUS: 'new',
							productId: productId,
							FILE_FORMAT: excelFileFormat
						});
						//判断该节点是否为结构装配
						var isStructuralAssembly = me.isStructuralAssembly({
							treeid: treeid /* NUMBER */
						});
						if (isStructuralAssembly === 'true') {
							if (tag[fileNameClo].indexOf('精度测量') > -1) {
								var fileUploadPath = Things["Thing.Fn.SystemDic"].getFileUploadPath();
								var result_id = excel_resultId;
								var data_id = source_id;
								var product_id = productId;
								var ref_dpid = row.NODECODE;
								var thisFilePath = fileUploadPath + excelFilePath;
								var model = me.getModelByTreeid({
									treeid: treeid /* NUMBER */
								});
								me.parseStructuralAssemblyFile({
									ref_dpid: ref_dpid /* NUMBER */ ,
									data_id: data_id /* NUMBER */ ,
									result_id: result_id /* NUMBER */ ,
									product_id: product_id /* NUMBER */ ,
									model: model /* STRING */ ,
									thisFilePath: thisFilePath /* STRING */
								});
							}
						}
					} else if (entype == "MaterialDelivery") {
						var materieResultId = me.getMaterieResultId({
							tableType: tableType,
							nodecode: row.NODECODE,
							materielCode: tag[fileNameClo]
						});
						if (materieResultId == -1) {
							resultId = me.addDataToResultTable({
								type: tableType,
								NODECODE: row.NODECODE,
								NODENAME: row.NODECODE,
								FILE_NUMBER: "",
								FILE_NAME: tag[fileNameClo],
								FILE_TYPE: type,
								GATHERING_METHOD: "自动采集",
								SOURCE_SYSTEM: "MES",
								DELIVERY_STATE: row.DELIVERY_STATE,
								SECURITY_LEVEL: tag.SecurityLevel,
								FILEPATH: filePath,
								FILENAME: fileName,
								STATE_CHECK: "未确认",
								STATUS: 'new',
								productId: productId,
								FILE_FORMAT: fileFormat
							});
						} else {
							resultId = materieResultId;
						}
					} else {
						resultId = me.addDataToResultTable({
							type: tableType,
							NODECODE: row.NODECODE,
							NODENAME: row.NODECODE,
							FILE_NUMBER: "",
							FILE_NAME: tag[fileNameClo],
							FILE_TYPE: type,
							GATHERING_METHOD: "自动采集",
							SOURCE_SYSTEM: "MES",
							DELIVERY_STATE: row.DELIVERY_STATE,
							SECURITY_LEVEL: tag.SecurityLevel,
							FILEPATH: filePath,
							FILENAME: fileName,
							STATE_CHECK: "未确认",
							STATUS: 'new',
							productId: productId,
							FILE_FORMAT: fileFormat
						});
					}
				} else { //存在为更新
					var existRow = rs.rows[0];
					var soId = existRow['SOURCE_ID'];
					if (entype == "CheckCard") {
						pdf_resultId = existRow['PDF_RESULT_ID'];
						//先删除原有数据
						Things["Thing.DB.Oracle"].RunCommand({
							sql: 'delete from ' + tableType + ' where id=' + pdf_resultId
						});
						//再增加原有id的数据
						me.updateDataToResultTable({
							id: pdf_resultId,
							type: tableType,
							NODECODE: row.NODECODE,
							NODENAME: row.NODECODE,
							FILE_NUMBER: "",
							FILE_NAME: tag[fileNameClo],
							FILE_TYPE: type,
							GATHERING_METHOD: "自动采集",
							SOURCE_SYSTEM: "MES",
							DELIVERY_STATE: row.DELIVERY_STATE,
							SECURITY_LEVEL: tag.SecurityLevel,
							FILEPATH: pdfFilePath,
							FILENAME: pdfFileName,
							STATE_CHECK: "未确认",
							STATUS: 'new',
							productId: productId,
							FILE_FORMAT: pdfFileFormat
						});
						excel_resultId = existRow['EXCEL_RESULT_ID'];
						//先删除原有数据
						Things["Thing.DB.Oracle"].RunCommand({
							sql: 'delete from ' + tableType + ' where id=' + excel_resultId
						});
						//再增加原有id的数据
						me.updateDataToResultTable({
							id: excel_resultId,
							type: tableType,
							NODECODE: row.NODECODE,
							NODENAME: row.NODECODE,
							FILE_NUMBER: "",
							FILE_NAME: tag[fileNameClo],
							FILE_TYPE: type,
							GATHERING_METHOD: "自动采集",
							SOURCE_SYSTEM: "MES",
							DELIVERY_STATE: row.DELIVERY_STATE,
							SECURITY_LEVEL: tag.SecurityLevel,
							FILEPATH: excelFilePath,
							FILENAME: excelFileName,
							STATE_CHECK: "未确认",
							STATUS: 'new',
							productId: productId,
							FILE_FORMAT: excelFileFormat
						});

						//判断该节点是否为结构装配
						var isStructuralAssembly = me.isStructuralAssembly({
							treeid: treeid /* NUMBER */
						});
						if (isStructuralAssembly === 'true') {
							if (tag[fileNameClo].indexOf('精度测量') > -1) {
								var fileUploadPath = Things["Thing.Fn.SystemDic"].getFileUploadPath();
								var result_id = excel_resultId;
								var data_id = soId;
								var product_id = productId;
								var ref_dpid = row.NODECODE;
								var thisFilePath = fileUploadPath + excelFilePath;
								var model = me.getModelByTreeid({
									treeid: treeid /* NUMBER */
								});
								Things["Thing.DB.Oracle"].RunCommand({
									sql: "delete from TJFX_STRUCTURALASSEMBLY where RESULT_ID=" + result_id
								});
								me.parseStructuralAssemblyFile({
									ref_dpid: ref_dpid /* NUMBER */ ,
									data_id: data_id /* NUMBER */ ,
									result_id: result_id /* NUMBER */ ,
									product_id: product_id /* NUMBER */ ,
									model: model /* STRING */ ,
									thisFilePath: thisFilePath /* STRING */
								});
							}
						}
					} else {
						resultId = existRow['RESULT_ID'];
						//先删除原有数据
						Things["Thing.DB.Oracle"].RunCommand({
							sql: 'delete from ' + tableType + ' where id=' + resultId
						});
						//再增加原有id的数据
						me.updateDataToResultTable({
							id: resultId,
							type: tableType,
							NODECODE: row.NODECODE,
							NODENAME: row.NODECODE,
							FILE_NUMBER: "",
							FILE_NAME: tag[fileNameClo],
							FILE_TYPE: type,
							GATHERING_METHOD: "自动采集",
							SOURCE_SYSTEM: "MES",
							DELIVERY_STATE: row.DELIVERY_STATE,
							SECURITY_LEVEL: tag.SecurityLevel,
							FILEPATH: filePath,
							FILENAME: fileName,
							STATE_CHECK: "未确认",
							STATUS: 'new',
							productId: productId,
							FILE_FORMAT: fileFormat
						});
					}
					//删除业务表数据，稍后再新增
					Things["Thing.DB.Oracle"].RunCommand({
						sql: "delete from XMLDATA_" + entype + " where SOURCE_ID='" + soId + "'"
					});
				}
				//增加业务数据
				me.insertXmlData({
					type: entype,
					tag: tag,
					source_id: source_id,
					resultId: resultId,
					excelFileName: excelFileName,
					excelFilePath: excelFilePath,
					pdfFileName: pdfFileName,
					pdfFilePath: pdfFilePath,
					fileName: fileName,
					filePath: filePath,
					pdf_resultId: pdf_resultId,
					excel_resultId: excel_resultId,
					productId: productId
				});
				rsLen++;
			}
		}
	}
} catch (e) {
	logger.error('automaticCollect-error:' + e);
	result = '成功同步' + rsLen + '条数据后，中断操作日志：' + e;
}
var endTime = new Date().getTime();

var time = endTime - startTime;
var timeStr = me.msecToTime({
	msec: time
});
if (result === '') {
	result = '成功同步' + rsLen + '条数据，耗时：' + timeStr + '!';
}