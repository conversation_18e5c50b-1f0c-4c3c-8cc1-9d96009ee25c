/**
 * @definition    downloadFileV2
 * @description   下载文件V2（统一超时/重试/返回结构）  wanghq
 * @implementation    {Script}
 *
 * @param    {STRING}    url
 *
 * @returns    {JSON}
 */
var resultJson = {
	success: false,
	fileName: "",
	filePath: "",
	fileFormat: "",
	message: ""
};

try {
	if (url == null || url === "") {
		result = resultJson;
	} else {
		var u1 = encodeURIComponent(url);
		var reqUrl = "http://127.0.0.1:8011/FileHandle/table/download/mes/file?url=" + u1;
		var params = {
			url: reqUrl,
			timeout: 120000
		};
		var httpRs = Resources["ContentLoaderFunctions"].PostJSON(params);
		if (httpRs && httpRs.success === true) {
			resultJson.success = true;
			resultJson.fileName = httpRs.fileName || "";
			resultJson.filePath = httpRs.filePath || "";
			resultJson.fileFormat = httpRs.fileFormat || "";
		} else {
			resultJson.message = httpRs && httpRs.message ? httpRs.message : "download failed";
		}
		result = resultJson;
	}
} catch (e) {
	resultJson.message = String(e);
	result = resultJson;
}


