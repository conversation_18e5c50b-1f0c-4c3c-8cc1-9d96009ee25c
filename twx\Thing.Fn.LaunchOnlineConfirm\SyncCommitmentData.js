/**
 * @definition    SyncCommitmentData
 * @description   wanghq 2024年11月28日10:49:23
 * @implementation    {Script}
 *
 * @param    {STRING}    nodeId    
 * @param    {STRING}    username        {"aspect.defaultValue":"adm"}
 *
 * @returns    {JSON}
 */
var res = {};
try {
    me.CreateSyncStatus({ nodeId: nodeId, username: username });
    //查询分级承诺节点以及分级承诺下的所有B表
    var allNode = Things['Thing.DB.Oracle'].RunQuery({ sql: me.GetAllCommitmentNodeSql({ nodeId: nodeId }) }).rows;

    var syncCount = 0;
    var syncResults = [];
    var totalCount = 0;

    //遍历allNode下的所有B表 根据映射关系表建立关系
    for (var i = 1; i < allNode.length; i++) {
        var node = allNode[i];
        var tableId = node['ID'];
        var tableName = node['NAME'];
        var aitTableId = node['QUALITY_REPORT_ID'];
        var nodeSyncStatus = node['SYNC_STATUS'];

        totalCount++;

        var syncResult = {
            tableId: tableId,
            tableName: tableName,
            aitTableId: aitTableId,
            syncStatus: 'FAILED',
            errorMessage: ''
        };

        if (nodeSyncStatus == 'ACTIVE') {
            try {
                var aitSql = "select * from quality_report where ID=" + aitTableId;
                var aitRows = Things['Thing.DB.Oracle'].RunQuery({ sql: aitSql }).rows;
                if (aitRows.length > 0) {
                    var aitData = aitRows[0];
                    var dataSource = aitData['DATA_SOURCE'];
                    var reportType = aitData['REPORT_TYPE'];
                    var dataType = aitData['DATA_TYPE'];
                    var aitTableName = aitData['NAME'];
                    var treeId = aitData['TREE_ID'];

                    syncResult.aitTableName = aitTableName;
                    syncResult.dataSource = dataSource;

                    // 获取AIT表的专业节点和过程节点信息
                    try {
                        var treeSql = "select DIR_NAME, LEAF_NAME from TREE_ALL_VIEW where TREEID = " + treeId;
                        var treeRows = Things['Thing.DB.Oracle'].RunQuery({ sql: treeSql }).rows;
                        if (treeRows.length > 0) {
                            syncResult.aitProfessionalNode = treeRows[0]['DIR_NAME'] || '';
                            syncResult.aitProcessNode = treeRows[0]['LEAF_NAME'] || '';
                        } else {
                            syncResult.aitProfessionalNode = '';
                            syncResult.aitProcessNode = '';
                        }
                    } catch (treeError) {
                        syncResult.aitProfessionalNode = '';
                        syncResult.aitProcessNode = '';
                        logger.warn("获取AIT表节点信息失败：" + treeError);
                    }

                    // 调用公共的单表同步服务
                    var syncRes = Things['Thing.Fn.LaunchOnlineConfirm'].SyncSingleTable({
                        tableId: tableId,
                        aitTableId: aitTableId,
                        username: username
                    });

                    if (syncRes.success) {
                        //更新LAST_SYNC_TIME 和 LAST_SYNC_USER
                        var updateLastSyncSql = "update COMMITMENT_SYNC_RELATION set LAST_SYNC_TIME = SYSDATE, LAST_SYNC_USER = '" + username + "' where COMMITMENT_TABLE_ID = " + tableId;
                        Things['Thing.DB.Oracle'].RunCommand({ sql: updateLastSyncSql });

                        syncResult.syncStatus = 'SUCCESS';
                        syncCount++;
                    } else {
                        syncResult.errorMessage = syncRes.msg;
                    }
                } else {
                    syncResult.errorMessage = '找不到对应的AIT质量确认表';
                }
            } catch (tableError) {
                syncResult.errorMessage = '处理表时出错：' + tableError;
                logger.error("同步表失败：" + tableName + "，错误：" + tableError);
            }
        } else {
            syncResult.errorMessage = '同步状态不是ACTIVE，跳过同步';
        }

        syncResults.push(syncResult);
    }

    res.success = true;
    res.msg = "同步完成，成功：" + syncCount + "条，失败：" + (totalCount - syncCount) + "条";
    res.data = {
        syncResults: syncResults,
        syncCount: syncCount,
        totalCount: totalCount
    };
} catch (error) {
    res.success = false;
    res.msg = "同步失败，原因：" + error;
    logger.error("SyncCommitmentData失败：" + error);
}
result = res;